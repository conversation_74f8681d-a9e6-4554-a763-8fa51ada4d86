from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ALTER COLUMN "recording_url" TYPE VARCHAR(500) USING "recording_url"::VARCHAR(500);"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "live_session" ALTER COLUMN "recording_url" TYPE VARCHAR(255) USING "recording_url"::VARCHAR(255);"""
