name: CI

on:
  release:
    types: [published]

permissions:
  contents: read
  id-token: write
  pull-requests: write


jobs:
  build_and_push:
    name: <PERSON>uild and Push Docker Image
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.get_version.outputs.version }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up SSH Agent
        uses: webfactory/ssh-agent@v0.5.1
        with:
          ssh-private-key: ${{ secrets.CIBA_PARTICIPANT_SSH_PRIVATE }}
      - name: Install uv
        uses: astral-sh/setup-uv@v2
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version-file: ".python-version"
      - name: Get project version
        id: get_version
        run: |
          echo "version=$(uv run python src/__version__.py)" >> $GITHUB_ENV
          echo "::set-output name=version::$(uv run python src/__version__.py)"

      - uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.OLD_AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.OLD_AWS_SECRET_KEY }}
          aws-region: us-east-2


      - name: Log in to AWS ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Build Dev Docker Image
        if: github.event_name == 'pull_request' || github.event_name == 'push'
        run: |
          DOCKER_BUILDKIT=1 docker build --ssh default \
            --target dev \
            --build-arg INSTALL_DEV=true \
            -t ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:dev-${{ env.version }}-${{ github.sha }} .
          docker tag ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:dev-${{ env.version }}-${{ github.sha }} \
          ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:dev-latest

      - name: Build Prod Docker Image
        if: github.event_name == 'release'
        run: |
          DOCKER_BUILDKIT=1 docker build --ssh default \
            --target prod \
            --build-arg INSTALL_DEV=false \
            -t ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }} .
          docker tag ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }} \
          ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:latest

      - name: Tag and Push Dev Image
        if: github.event_name == 'push'
        run: |
          docker push ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:dev-${{ env.version }}-${{ github.sha }}
          docker push ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:dev-latest

      - name: Tag and Push Prod Image
        if: github.event_name == 'release'
        run: |
          docker push ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:${{ env.version }}-${{ github.sha }}
          docker push ${{ vars.DOCKER_REGISTRY }}/${{ vars.APP_NAME }}:latest

  deploy_prod:
    name: Deploy to Prod
    needs: build_and_push
    if: github.event_name == 'release'
    uses: ./.github/workflows/deploy.yaml
    with:
      environment: production
      image_tag: ${{ needs.build_and_push.outputs.version }}-${{ github.sha }}
      k8s_env: prod
    secrets:
      CI_USERNAME: ${{ secrets.CI_USERNAME }}
      CI_PUSH_TOKEN: ${{ secrets.CI_PUSH_TOKEN }}
      CI_USER_EMAIL: ${{ secrets.CI_USER_EMAIL }}
