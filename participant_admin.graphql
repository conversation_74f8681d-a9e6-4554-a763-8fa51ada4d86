type ActivityHistoryItem {
  week: Int!
  date: DateTime!
  physicalActivityMinutes: Float!
}

enum ActivityUnit {
  ACTION
  KG
  LB
}

type AddressType {
  city: String!
  state: String!
  street1: String!
  street2: String
  zipCode: String!
}

input AssignParticipantChatInput {
  participantId: UUID!
  cohortId: UUID!
  type: String!
}

input AuthorizedCreateInput {
  email: String!
  firstName: String!
  lastName: String!
  role: AutorizedRole! = HEALTH_COACH
}

type AuthorizedListType {
  authorized: [AuthorizedType!]!
}

type AuthorizedType {
  id: UUID!
  email: String!
  firstName: String!
  lastName: String!
  chatIdentity: String!
  role: AutorizedRole!
  status: ParticipantStatus!
  cognitoSub: UUID
  isTest: Boolean
  apiId: UUID
  supportInChat: Boolean
  classesAdmin: Boolean
  contentAdmin: Boolean!
  cohortAdmin: Boolean!
  createdAt: DateTime!
  updatedAt: DateTime!
}

input AuthorizedUpdateInput {
  id: UUID!
  email: String = null
  firstName: String = null
  lastName: String = null
  role: AutorizedRole = null
  status: ParticipantStatus = null
}

enum AutorizedRole {
  ADMIN
  HEALTH_COACH
  PROVIDER
}

enum BookingStatusEnum {
  BOOKED
  ATTENDED
  WATCHED_RECORDING
  CANCELED
}

type BookingType {
  id: UUID!
  status: BookingStatusEnum!
  participant: ParticipantType
}

type CarrierData {
  name: String!
  trackingLinkTemplate: String!
}

type ClassCreatorType {
  success: Boolean!
  creators: [ProviderType!]!
  error: String
}

input CohortIDInput {
  id: UUID!
}

type CohortInfoType {
  id: UUID!
  name: String!
  programId: UUID!
  limit: Int!
  createdAt: DateTime!
  updatedAt: DateTime!
  program: ProgramType!
  createdBy: AuthorizedType!
  currentWeek: String
  currentModule: String
  totalWeeks: Int!
}

input CohortMemberCreateInput {
  cohortId: UUID!
  participantId: UUID!
}

input CohortMemberIDInput {
  cohortId: UUID!
  participantId: UUID!
}

type CohortMemberType {
  createdAt: DateTime!
  updatedAt: DateTime!
  cohortId: UUID!
  participantId: UUID!
}

type CohortMembersListType {
  cohortMembers: [CohortMemberType!]!
}

enum CohortStateFilter {
  ALL
  EMPTY
  NOT_EMPTY
}

enum CohortStatusFilter {
  ALL
  ACTIVE
  ENDING
  COMPLETED
  PENDING
}

enum CohortStatusGraphEnum {
  ACTIVE
  COMPLETED
  ENDING
  PENDING
}

type CohortType {
  id: UUID!
  createdAt: DateTime!
  updatedAt: DateTime!
  limit: Int
  status: CohortStatusGraphEnum
  endDate: DateTime
  name: String
  startedAt: DateTime
  programId: UUID!
  participants: [ParticipantType!]!
  isEditable: Boolean!
  hasParticipants: Boolean!
  createdBy: AuthorizedType
  program: ProgramType
}

type CohortsListType {
  cohorts: [CohortType!]!
  totalPages: Int!
}

type ContentMaterialElement {
  id: UUID!
  addedAt: DateTime!
  mimeType: String!
  title: String!
  description: String!
  status: ContentMaterialStatus!
  programs: [RelatedProgram!]!
  tags: [MaterialTag!]!
  activityTypes: [ParticipantActivityEnum!]!
  link: String
  linkExpiration: DateTime
  fileName: String
  fileSize: Int
  formId: String
}

type ContentMaterialList {
  success: Boolean!
  error: String
  items: [ContentMaterialElement!]!
  total: Int!
  totalPages: Int!
}

enum ContentMaterialStatus {
  ACTIVE
  DELETED
  ARCHIVED
}

type ConversationDneError {
  message: String!
}

type ConversationType {
  id: UUID!
  type: String!
  sid: String!
  uniqueName: String!
  friendlyName: String!
  createdAt: String!
  updatedAt: String!
}

union ConversationTypeConversationDneError = ConversationType | ConversationDneError

input CreateClassInput {
  topic: TopicEnum!
  title: String!
  description: String!
  maxCapacity: Int!
  timezone: String!
  meetingStartTime: DateTime!
  numberOfSessions: Int!
  recurrence: RecurrenceEnum!
  hostId: UUID!
  duration: Int = 60
  coverUrl: String = null
}

input CreateCohortInput {
  name: String!
  startDate: DateTime!
  limit: Int!
  programId: UUID!
}

type CreateCohortOutput {
  id: UUID!
  name: String!
  limit: Int!
  startedAt: DateTime!
  programId: UUID!
  createdBy: UUID!
}

"""Date (isoformat)"""
scalar Date

input DateRangeInput {
  start: DateTime!
  end: DateTime!
}

"""Date with time (isoformat)"""
scalar DateTime

type DayWeightItem {
  weight: Float
  source: String
  date: Date!
}

type DetailedResponse {
  success: Boolean!
  message: String
  errorCode: String
}

type DeviceInfo {
  id: String!
  deviceType: String!
  lastSyncedAt: DateTime!
}

type DeviceStatus {
  status: DeviceStatusEnum!
  device: DeviceTypeEnum!
  battery: Int
  signal: Int
}

enum DeviceStatusEnum {
  NOT_CONNECTED
  CONNECTED
  RECONNECT
}

enum DeviceTypeEnum {
  WITHINGS
  FITBIT
  DEXCOM
  OURARING
  TRANSTEK
}

enum FieldEnum {
  first_name
  last_name
  email
  cohort_name
  created_by_name
  program_title
  current_week
}

enum FullBookingStatusGraphEnum {
  BOOKED
  ATTENDED
  WATCHED_RECORDING
  CANCELED
  MISSED
}

input GetCohortsFilterInput {
  nameLike: String = null
  createdBy: UUID = null
  programId: UUID = null
  cohortState: CohortStateFilter! = ALL
  cohortStatus: CohortStatusFilter! = ALL
}

input GetParticipantsFilterInput {
  search: String = null
  createdById: UUID = null
  cohortId: UUID = null
  programId: UUID = null
  dateEnrolledRange: DateRangeInput = null
  participantStatus: ParticipantStatus = null
}

input GetParticipantsSortInput {
  field: FieldEnum!
  order: OrderEnum!
}

input GetProgramInput {
  id: UUID!
}

input GetProgramMediaInput {
  sectionId: UUID!
  contentType: String!
  fileName: String!
}

input GetProvidersFilterInput {
  role: AutorizedRole!
}

type HasActivityRecordError {
  message: String!
}

input HeadsUpParticipantCreateInput {
  participantId: UUID!
  headsUpToken: String
  headsUpId: String
}

input HeadsUpParticipantIDInput {
  id: UUID!
}

type HeadsUpParticipantType {
  id: UUID!
  participantId: UUID!
  headsUpToken: String
  headsUpId: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

input HeadsUpParticipantUpdateInput {
  id: UUID!
  participantId: UUID
  headsUpToken: String
  headsUpId: String
}

type HeadsUpParticipantsListType {
  participants: [HeadsUpParticipantType!]!
}

type HeightType {
  feet: Float!
  inches: Float!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](https://ecma-international.org/wp-content/uploads/ECMA-404_2nd_edition_december_2017.pdf).
"""
scalar JSON @specifiedBy(url: "https://ecma-international.org/wp-content/uploads/ECMA-404_2nd_edition_december_2017.pdf")

type LatestMeasures {
  success: Boolean!
  error: String
  lastCibaSync: DateTime
  lastDeviceSync: DateTime
  devices: [DeviceInfo!]!
  measures: [Measure!]!
}

type LiveSessionListType {
  liveSessions: [LiveSessionType!]!
  success: Boolean!
  error: String
}

type LiveSessionType {
  id: UUID!
  title: String!
  description: String!
  meetingStartTime: DateTime!
  hostId: UUID
  topic: TopicEnum
  timezone: String!
  hasConflict: Boolean!
  meetingType: MeetingTypeEnum!
  zoomId: String!
  zoomOccurrenceId: String!
  zoomLink: String!
  recordingUrl: String
  useCustomMeetingLink: Boolean!
  customMeetingLink: String
  maxCapacity: Int
  bookingsCount: Int
  bookings: [BookingType!]
  webinarId: UUID
}

input LiveSessionUpdateInput {
  title: String = null
  description: String = null
  startTime: DateTime = null
  timezone: String = null
  useCustomMeetingLink: Boolean = null
  customMeetingLink: String = null
}

type MaterialCreationResponse {
  id: UUID
  success: Boolean!
  error: String
  uploadUrl: String
  fields: String
}

input MaterialData {
  title: String!
  description: String!
  mimeType: String!
  contentUrl: String = null
  fileName: String = null
  fileSize: Int = 0
  formId: String = null
  activityTypes: [ParticipantActivityEnum!]!
  tags: [MaterialTag!]!
  programs: [UUID!]!
}

input MaterialFilters {
  tags: [MaterialTag!] = null
  activityTypes: [ParticipantActivityEnum!] = null
  programs: [UUID!] = null
  status: ContentMaterialStatus = null
  search: String = null
}

type MaterialSimpleResponse {
  id: UUID
  success: Boolean!
  error: String
}

enum MaterialTag {
  ACTIVITY
  GUT_HEALTH
  HEALTHY_EATING
  MINDSETS
  MOTIVATION
  RECIPES
  SLEEP
  STRESS
  SUPPORT
}

type MeType {
  user: UserType!
  authorized: AuthorizedType
}

type Measure {
  value: Float!
  unit: String!
  createdAt: DateTime!
}

type MediaUploadUrlType {
  url: String!
  fields: String!
}

enum MeetingTypeEnum {
  INSTANT
  SCHEDULED
  RECURRING_WITH_FIXED_TIME
}

enum MetaType {
  FILE
  TYPE_FORM
  INPUT
  USER_INPUT
  ZOOM
  VIDEO
  URL
}

input MoveParticipantInput {
  participantId: UUID!
  oldCohortId: UUID!
  newCohortId: UUID!
}

type MoveParticipantOutput {
  participantId: UUID!
  cohortId: UUID!
}

union MoveParticipantOutputWrongCohort = MoveParticipantOutput | WrongCohort

type Mutation {
  createParticipantActivity(data: ParticipantActivityInput!): ParticipantActivityGraphType!
  updateParticipantActivity(data: ParticipantActivityUpdateInput!): ParticipantActivityGraphType!
  deleteParticipantActivity(data: ParticipantActivityIDInput!): SimpleRespType!
  createCohort(data: CreateCohortInput!, createdBy: UUID!): CreateCohortOutput!
  updateCohort(data: UpdateCohortInput!): CohortType!
  deleteCohort(data: CohortIDInput!): SimpleRespType!
  createCohortMember(data: CohortMemberCreateInput!): CohortMemberType!
  updateCohortMember(data: CohortMemberIDInput!, newCohortId: UUID!): CohortMemberType!
  deleteCohortMember(data: CohortMemberIDInput!): SimpleRespType!
  importPendingCohorts(data: ScheduleIdsInput): SimpleRespType!
  moveParticipant(data: MoveParticipantInput!): MoveParticipantOutputWrongCohort!
  endCohort(cohortId: UUID!): SimpleRespType!
  createProgram(data: ProgramCreateInput!): ProgramType!
  updateProgram(data: ProgramUpdateInput!): ProgramType!
  deleteProgram(id: UUID!): SimpleRespType!
  addProgramModule(data: ProgramModuleCreateInput!): ProgramModuleType!
  updateProgramModule(data: ProgramModuleUpdateInput!): ProgramModuleType!
  deleteProgramModule(data: ProgramModuleIDInput!): SimpleRespType!
  addProgramModuleSection(data: ProgramModuleSectionCreateInput!): ProgramModuleSectionType!
  updateProgramModuleSection(data: ProgramModuleSectionUpdateInput!): ProgramModuleSectionType!
  deleteProgramModuleSection(data: ProgramModuleSectionIDInput!): SimpleRespTypeHasActivityRecordError!
  createParticipant(data: ParticipantCreateInput!): ParticipantType!
  updateParticipant(data: ParticipantUpdateInput!): ParticipantType!
  updateParticipantEmail(participantId: String!, email: String!): SimpleRespType!
  deleteParticipant(data: ParticipantIDInput!): SimpleRespType!
  createSoleraParticipant(data: SoleraParticipantCreateInput!): SoleraParticipantType!
  updateSoleraParticipant(data: SoleraParticipantUpdateInput!): SoleraParticipantType!
  deleteSoleraParticipant(data: SoleraParticipantIDInput!): SimpleRespType!
  createHeadsUpParticipant(data: HeadsUpParticipantCreateInput!): HeadsUpParticipantType!
  updateHeadsUpParticipant(data: HeadsUpParticipantUpdateInput!): HeadsUpParticipantType!
  deleteHeadsUpParticipant(data: HeadsUpParticipantIDInput!): SimpleRespType!
  createParticipantMeta(data: ParticipantMetaCreateInput!): ParticipantMetaType!
  updateParticipantMeta(data: ParticipantMetaUpdateInput!): ParticipantMetaType!
  deleteParticipantMeta(data: ParticipantMetaIDInput!): SimpleRespType!
  createAuthorized(data: AuthorizedCreateInput!, password: String!): AuthorizedType!
  updateAuthorized(data: AuthorizedUpdateInput!): AuthorizedType!
  assignParticipantToChat(assignParticipantToChat: AssignParticipantChatInput!): ConversationTypeConversationDneError!
  mergeLatestData(participantId: UUID!): ShortResponse!
  pairDevice(participantId: UUID!, imei: String = null, serialNumber: String = null): DetailedResponse!
  unpairDevice(participantId: UUID!, deviceId: UUID!): DetailedResponse!
  createWebinar(input: CreateClassInput!, userId: UUID!): WebinarCRUDResponse!
  updateWebinar(input: UpdateClassInput!, userId: UUID!): WebinarCRUDResponse!
  deleteWebinar(classId: UUID!, userId: UUID!): WebinarCRUDResponse!
  updateLiveSession(liveSessionId: UUID!, data: LiveSessionUpdateInput!): SimpleRespType!
  deleteLiveSession(liveSessionId: UUID!): ShortResponse!
  uploadLiveSessionRecording(liveSessionId: UUID!): SimpleRespType!
  deleteLiveSessionRecording(liveSessionId: UUID!): SimpleRespType!
  uploadVideoRecording(liveSessionId: UUID!, videoUrl: String!): SimpleRespType!
  updateLiveSessionRecording(liveSessionId: UUID!, videoUrl: String!): SimpleRespType!
  addContentMaterial(materialData: MaterialData!): MaterialCreationResponse!
  deleteContentMaterial(materialId: UUID!): MaterialSimpleResponse!
  editContentMaterial(materialId: UUID!, materialData: MaterialData!): MaterialCreationResponse!
  updateArchiveStatus(materialId: UUID!): MaterialSimpleResponse!
  assignTranstekTrackingNumber(trackingNumber: String!, carrier: String!, imei: String = null, serialNumber: String = null): DetailedResponse!
}

enum OrderEnum {
  asc
  desc
}

input PaginationInput {
  page: Int! = 1
  perPage: Int! = 10
}

enum ParticipantActivityCategory {
  SYSTOLIC
  DIASTOLIC
  HEART_RATE
  WEIGHT
  BMI
  ACTIVITY
  STEPS
}

enum ParticipantActivityDevice {
  WITHINGS
  MANUAL_INPUT
  TRANSTEK
}

enum ParticipantActivityEnum {
  ENROLL
  WEIGHT
  PLAY
  COACH
  RECIPES
  QUIZ
  ACTIVITY
  ARTICLE
  GROUP
}

type ParticipantActivityGraphType {
  id: UUID!
  participantId: UUID!
  activityType: ParticipantActivityEnum!
  activityCategory: ParticipantActivityCategory!
  activityDevice: ParticipantActivityDevice!
  unit: ActivityUnit!
  createdAt: DateTime!
  value: String!
}

input ParticipantActivityIDInput {
  id: UUID!
}

input ParticipantActivityInput {
  participantId: UUID!
  value: String!
  unit: ActivityUnit!
  activityDevice: ParticipantActivityDevice!
  activityCategory: ParticipantActivityCategory!
  activityType: ParticipantActivityEnum!
}

type ParticipantActivityStats {
  lastActivity: DateTime
  totalModules: Int!
  completedModulesCount: Int!
  totalActivities: Int!
  completedActivitiesCount: Int!
  activityHistory: [ActivityHistoryItem!]!
  weightHistory: [DayWeightItem!]!
}

input ParticipantActivityUpdateInput {
  id: UUID!
  participantId: UUID = null
  value: Float = null
  unit: ActivityUnit = null
  activityDevice: ParticipantActivityDevice = null
  activityCategory: ParticipantActivityCategory = null
  activityType: ParticipantActivityEnum = null
}

type ParticipantClassProgressType {
  liveSession: LiveSessionType!
  participant: ParticipantType!
  host: AuthorizedType!
  status: FullBookingStatusGraphEnum!
  lastProgressDate: DateTime!
}

input ParticipantCreateInput {
  email: String!
  firstName: String!
  lastName: String!
  groupId: UUID!
  memberId: UUID!
  status: ParticipantStatus! = PENDING
  cognitoSub: UUID = null
  medicalRecord: String = null
  isTest: Boolean! = false
  lastReset: DateTime = null
}

type ParticipantDneError {
  message: String!
}

input ParticipantIDInput {
  id: UUID!
}

type ParticipantInfoType {
  id: UUID!
  email: String!
  firstName: String!
  lastName: String!
  chatIdentity: String!
  phone: String
  groupId: UUID!
  memberId: UUID!
  status: ParticipantStatus!
  cognitoSub: UUID
  medicalRecord: String
  isTest: Boolean!
  lastReset: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  lastActivityDate: DateTime
  dateEnrolled: DateTime
  cohort: CohortInfoType
  disenrolledReason: String
  disenrollmentDate: DateTime
  deviceStatus: [DeviceStatus!]!
}

input ParticipantMetaCreateInput {
  participantId: UUID!
  metadata: JSON!
}

input ParticipantMetaIDInput {
  id: UUID!
}

type ParticipantMetaType {
  id: UUID!
  participantId: UUID!
  metadata: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

input ParticipantMetaUpdateInput {
  id: UUID!
  participantId: UUID
  metadata: JSON!
}

type ParticipantMetasListType {
  participants: [ParticipantMetaType!]!
}

type ParticipantModuleProgressType {
  firstWeight: Float
  lastWeight: Float
  averageActivityMinutes: Int
  sections: [SectionWithActivity!]!
  classProgress: ProgressType!
  chatProgress: ProgressType!
  success: Boolean!
  error: String
}

type ParticipantNotInConversationError {
  message: String!
}

type ParticipantProgramProgressType {
  programModule: ProgramModuleType!
  totalActivities: Int!
  completedActivities: Int!
}

enum ParticipantStatus {
  ACTIVE
  COMPLETED
  PENDING
  REJECTED
  DELETED
}

type ParticipantType {
  id: UUID!
  email: String!
  firstName: String!
  lastName: String!
  chatIdentity: String!
  groupId: UUID!
  memberId: UUID!
  status: ParticipantStatus!
  statusInCohort: StatusInCohort
  cognitoSub: UUID
  medicalRecord: String
  soleraCareplanId: String
  isTest: Boolean!
  lastReset: DateTime
  createdAt: DateTime!
  updatedAt: DateTime!
  activities: [ParticipantActivityGraphType!]
  startingDate: DateTime
  age: Int
  gender: String
  currentModule: ProgramModuleType
  address: AddressType
  birthdate: Date
  initialWeight: Float
  height: HeightType
  phone: String
  currentWeek: String
  disenrolledReason: String
  disenrollmentDate: DateTime
  userReportedWeight: Float
  userTargetWeight: Float
  devicesStatus: [DeviceStatus!]
}

input ParticipantUpdateInput {
  id: UUID!
  email: String = null
  firstName: String = null
  lastName: String = null
  groupId: String = null
  memberId: String = null
  status: ParticipantStatus = null
  cognitoSub: UUID = null
  medicalRecord: String = null
  isTest: Boolean = null
  lastReset: DateTime = null
}

type ParticipantsInfoListType {
  participants: [ParticipantInfoType!]!
  totalPages: Int!
}

type ParticipantsListType {
  participants: [ParticipantType!]!
}

type PreviewDateListType {
  dates: [PreviewDateType!]!
}

type PreviewDateType {
  date: DateTime!
  hasConflict: Boolean!
}

input ProgramCreateInput {
  title: String!
  description: String = ""
}

input ProgramModuleCreateInput {
  title: String!
  shortTitle: String!
  length: Int!
  description: String!
  programId: UUID!
  order: Int!
}

input ProgramModuleIDInput {
  id: UUID!
}

input ProgramModuleSectionCreateInput {
  title: String!
  description: String!
  metadata: ProgramModuleSectionMetadataInput!
  programModuleId: UUID!
  activityType: ParticipantActivityEnum!
  activityCategory: ParticipantActivityCategory!
}

input ProgramModuleSectionIDInput {
  id: UUID!
}

input ProgramModuleSectionMetadataInput {
  startedAt: DateTime = null
  url: String = null
  formId: String = null
  type: MetaType = null
}

type ProgramModuleSectionMetadataType {
  url: String
  formId: String
  startedAt: DateTime
  type: MetaType
  signedUrl: String
}

type ProgramModuleSectionType {
  id: UUID!
  createdAt: DateTime!
  updatedAt: DateTime!
  title: String!
  description: String!
  programModuleId: UUID!
  activityType: ParticipantActivityEnum!
  activityCategory: ParticipantActivityCategory!
  metadata: ProgramModuleSectionMetadataType
}

input ProgramModuleSectionUpdateInput {
  id: UUID!
  title: String = null
  description: String = null
  metadata: ProgramModuleSectionMetadataInput = null
  programModuleId: UUID = null
  activityType: ParticipantActivityEnum = null
  activityCategory: ParticipantActivityCategory = null
}

type ProgramModuleType {
  id: UUID!
  createdAt: DateTime!
  updatedAt: DateTime!
  title: String!
  shortTitle: String!
  length: Int!
  description: String!
  programId: UUID!
  order: Int!
  startedAt: DateTime
  endedAt: DateTime
  cohortModuleId: UUID
  sections: [ProgramModuleSectionType!]!
}

input ProgramModuleUpdateInput {
  id: UUID!
  title: String = null
  shortTitle: String = null
  length: Int = null
  description: String = null
  programId: UUID = null
}

type ProgramType {
  id: UUID!
  createdAt: DateTime!
  updatedAt: DateTime!
  title: String!
  description: String!
  hasSections: Boolean!
  hasCohorts: Boolean!
  modules: [ProgramModuleType!]!
}

input ProgramUpdateInput {
  id: UUID!
  title: String = null
  description: String = null
}

type ProgramsType {
  programs: [ProgramType]
}

type ProgressType {
  completed: Boolean!
  activityDate: DateTime
}

type ProviderType {
  id: UUID!
  fullName: String!
  firstName: String!
  lastName: String!
  email: String!
  chatIdentity: String!
  description: String
  avatarUrl: String
  isAdmin: Boolean
  oldChatIdentity: String
}

type Query {
  me: MeType!
  getProviders(filters: GetProvidersFilterInput = null): AuthorizedListType!
  getCohortsCreators: AuthorizedListType!
  getAuthorizedUsers: AuthorizedListType!
  getParticipantActivitiesIds(participantId: UUID!): [UUID!]!
  getParticipantActivities: [ParticipantActivityGraphType!]!
  getParticipantActivity(data: ParticipantActivityIDInput!): ParticipantActivityGraphType
  getCohortIds(pagination: PaginationInput = {page: 1}): [UUID!]!
  getCohorts(pagination: PaginationInput = {page: 1}, filters: GetCohortsFilterInput = null): CohortsListType!
  getCohort(data: UUID!): CohortType
  getCohortMember(data: CohortMemberIDInput!): CohortMemberType
  getCohortMembers(data: CohortIDInput!): CohortMembersListType!
  getAllCohortMembers: CohortMembersListType!
  getCohortByParticipantId(data: ParticipantIDInput!): CohortType
  getParticipants: ParticipantsListType!
  getParticipant(data: ParticipantIDInput!): ParticipantType
  getSoleraParticipants: SoleraParticipantsListType!
  getSoleraParticipant(data: SoleraParticipantIDInput!): SoleraParticipantType!
  getHeadsUpParticipants: HeadsUpParticipantsListType!
  getHeadsUpParticipant(data: HeadsUpParticipantIDInput!): HeadsUpParticipantType!
  getParticipantMetas: ParticipantMetasListType!
  getParticipantMeta(data: ParticipantMetaIDInput!): ParticipantMetaType!
  listAuthorized: AuthorizedListType!
  getAuthorized(data: ParticipantIDInput!): AuthorizedType!
  getChatToken(chatIdentity: String!, adminId: String!): TokenTypeParticipantDneErrorParticipantNotInConversationError!
  getPaginatedParticipants(pagination: PaginationInput = {page: 1}, filters: GetParticipantsFilterInput = null, sort: GetParticipantsSortInput = null): ParticipantsInfoListType!
  getParticipantModuleProgress(participantId: UUID!, cohortModuleId: UUID!): ParticipantModuleProgressType
  getParticipantProgramProgress(participantId: UUID!, cohortId: UUID!): [ParticipantProgramProgressType!]
  getParticipantActivityStats(participantId: UUID!): ParticipantActivityStats
  getParticipantClassesProgress(participantId: UUID!, startDate: DateTime = null, endDate: DateTime = null): [ParticipantClassProgressType!]
  getLatestMeasures(participantId: UUID!): LatestMeasures!
  getParticipantDevices(participantId: UUID!): [DeviceStatus!]!
  getTranstekDeviceInfo(participantId: UUID!): TranstekDeviceInfoType!
  getProgramIds: [UUID!]!
  getPrograms: ProgramsType!
  getProgram(data: GetProgramInput!): ProgramType
  getMediaUploadUrl(data: GetProgramMediaInput!): MediaUploadUrlType!
  getProgramModules: [ProgramModuleType!]!
  getProgramModule(data: ProgramModuleIDInput!): ProgramModuleType
  getProgramModuleSections: [ProgramModuleSectionType!]!
  getProgramModuleSection(data: ProgramModuleSectionIDInput!): ProgramModuleSectionType
  getClassesCreators: ClassCreatorType!
  getWebinars(pagination: PaginationInput = {page: 1}, filters: WebinarFilterInput = null, timezone: String = "America/Los_Angeles"): WebinarListType!
  getWebinar(webinarId: UUID!): WebinarType!
  getParticipantsPerSession(liveSessionId: UUID!): [ParticipantType!]!
  getLiveSessions(startDate: DateTime!, endDate: DateTime!, timezone: String = "UTC", filters: WebinarFilterInput = null): LiveSessionListType!
  getPreviewDates(data: RecurrentDatesInput!): PreviewDateListType!
  getContentMaterial(page: Int! = 1, perPage: Int! = 10, filters: MaterialFilters = null): ContentMaterialList!
  getCarrierList: [CarrierData!]!
}

enum RecurrenceEnum {
  WEEKLY
  BI_WEEKLY
  TRI_WEEKLY
  MONTHLY
}

input RecurrentDatesInput {
  startingFrom: DateTime!
  timezone: String!
  count: Int!
  recurrence: RecurrenceEnum!
}

type RelatedProgram {
  id: UUID!
  title: String!
}

input ScheduleIdsInput {
  scheduleIds: [Int!] = null
}

type SectionWithActivity {
  section: ProgramModuleSectionType!
  completed: Boolean!
  activityDate: DateTime
}

type ShortResponse {
  success: Boolean!
  error: String
}

type SimpleRespType {
  status: Boolean!
}

union SimpleRespTypeHasActivityRecordError = SimpleRespType | HasActivityRecordError

input SoleraParticipantCreateInput {
  participantId: UUID!
  soleraId: UUID
  soleraKey: String!
  soleraProgramId: String!
  soleraEnrollmentId: String!
}

input SoleraParticipantIDInput {
  participantId: UUID!
}

type SoleraParticipantType {
  id: UUID!
  participantId: UUID!
  soleraId: UUID
  soleraKey: String!
  soleraProgramId: String!
  soleraEnrollmentId: String!
  createdAt: DateTime!
  updatedAt: DateTime!
}

input SoleraParticipantUpdateInput {
  id: UUID!
  participantId: UUID
  soleraId: UUID
  soleraKey: String
  soleraProgramId: String
  soleraEnrollmentId: String
}

type SoleraParticipantsListType {
  participants: [SoleraParticipantType!]!
}

enum StatusInCohort {
  INACTIVE
  AWAITING
  ACTIVE
  FINISHED
}

enum TimeOfDayEnum {
  MORNING
  AFTERNOON
  EVENING
}

type TokenType {
  token: String!
  chatIdentity: String!
}

union TokenTypeParticipantDneErrorParticipantNotInConversationError = TokenType | ParticipantDneError | ParticipantNotInConversationError

enum TopicEnum {
  FOOD
  EDUCATIONAL
  ACTIVITY
  HEALTH_AND_WELLNESS
  MENTAL_HEALTH
  INTRO_SESSION
}

type TranstekDeviceInfoType {
  id: String!
  serialNumber: String!
  imei: String!
  model: String!
  deviceType: String!
  status: String!
  trackingNumber: String
  carrier: String
  trackingUrl: String
  timezone: String
  lastStatusReport: String
  memberId: String
}

scalar UUID

input UpdateClassInput {
  id: UUID!
  topic: TopicEnum = null
  maxCapacity: Int = null
  meetingStartTime: DateTime = null
  title: String = null
  description: String = null
  duration: Int = null
  coverUrl: String = null
  newHost: String = null
}

input UpdateCohortInput {
  id: UUID!
  name: String = null
  startDate: DateTime = null
  limit: Int = null
  programId: UUID = null
}

type UserType {
  id: UUID!
  email: String!
  emailVerified: Boolean!
}

type WebinarCRUDResponse {
  webinarId: UUID
  success: Boolean!
  error: String
}

input WebinarFilterInput {
  titleLike: String = null
  topic: TopicEnum = null
  webinarHost: UUID = null
  timeOfDay: TimeOfDayEnum = null
}

type WebinarListType {
  webinars: [WebinarType!]!
  totalPages: Int!
}

type WebinarType {
  id: UUID!
  topic: TopicEnum!
  title: String!
  description: String!
  recurrence: RecurrenceEnum!
  host: AuthorizedType
  duration: Int!
  maxCapacity: Int!
  coverUrl: String
  featuredSession: LiveSessionType
  liveSessions: [LiveSessionType!]!
}

type WrongCohort {
  msg: String!
}
