from uuid import UUID

from ciba_participant.cohort.crud import CohortMembersRepository
from ciba_participant.cohort.models import CohortMembers, CohortMembershipStatus
from ciba_participant.common.db import init_db, close_db
from ciba_participant.notifications.email.template_info import (
    CLASS_CANCELED_TEMPLATE_SUBJECT,
)
from ciba_participant.participant.models import ParticipantStatus
from ciba_participant.settings import get_settings

settings = get_settings()


async def main():
    await init_db()

    # cohorts = await CohortRepository.get_paginated_cohorts(
    #     page=1,
    #     per_page=10,
    #     include={
    #         Include.program_modules,
    #         Include.participants,
    #         Include.program,
    #         Include.created_by,
    #     },
    #     filters=FilterInput(
    #         cohort_status=CohortStatusFilter.ending,
    #     ),
    # )
    #
    # total_pages = cohorts.total_pages
    #
    # print("=" * 80)
    # print("COHORTS DEBUG OUTPUT")
    # print("=" * 80)
    # print(f"Total Pages: {total_pages}")
    # print()
    #
    # for i, cohort in enumerate(cohorts.cohorts, 1):
    #     print(f"[{i}] COHORT: {cohort.name}")
    #     print("-" * 60)
    #     print(f"  ID: {cohort.id}")
    #     print(f"  Status: {cohort.status.value}")
    #     print(f"  Created: {cohort.created_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Updated: {cohort.updated_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Start Date: {cohort.started_at.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  End Date: {cohort.end_date.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    #     print(f"  Participant Limit: {cohort.limit}")
    #     print(f"  Current Participants: {len(cohort.participants)}")
    #     print()
    #
    #     print("=" * 80)
    #     print()

    # session_zoom_ids = await LiveSession.filter(zoom_id__isnull=False).values_list(
    #     "zoom_id", flat=True
    # )
    # session_zoom_ids = set(session_zoom_ids)
    # api_endpoint = settings.SCHEDULE_MANAGER_API_ENDPOINT
    # email_to_remove = "<EMAIL>"
    #
    # async with get_client(api_endpoint) as client:
    #     for zoom_id in session_zoom_ids:
    #         try:
    #             response = await client.get(f"/meetings/{zoom_id}")
    #             json_data = process_response_data(response)
    #
    #             emails = json_data["settings"].get("alternative_hosts", "")
    #             email_list = [
    #                 email.strip() for email in emails.split(";") if email.strip()
    #             ]
    #
    #             # Skip if email to remove is not in the list
    #             if email_to_remove not in email_list:
    #                 print(f"No update needed for meeting {zoom_id}")
    #                 continue
    #
    #             # Remove the email and rejoin
    #             filtered_emails = ";".join(
    #                 email for email in email_list if email != email_to_remove
    #             )
    #
    #             updates = {"settings": {"alternative_hosts": filtered_emails}}
    #
    #             response = await client.patch(f"/meetings/{zoom_id}", json=updates)
    #             process_response_data(response)
    #             print(f"Updated meeting {zoom_id}: removed {email_to_remove}")
    #
    #         except Exception as e:
    #             print(f"Failed to update meeting {zoom_id}: {e}")

    # print(
    #     await get_single_device_status(
    #         participant_id="a13bd580-70e1-70ac-ebdf-b62f0692a34f",
    #         device_type=DeviceTypeEnum.TRANSTEK,
    #     )
    # )

    print(await CohortMembersRepository.get_cohort_member(UUID("619bc5f0-80c1-70e9-6e00-a15198d074a2")))

    await close_db()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
