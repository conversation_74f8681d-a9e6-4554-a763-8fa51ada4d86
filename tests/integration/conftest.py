import pytest
from tortoise import Tortoise
from tortoise.contrib.test import (
    _init_db,
    getDBConfig,
)

MODULES = [
    "aerich.models",
    "ciba_participant.activity.models",
    "ciba_participant.classes.models",
    "ciba_participant.cohort.models",
    "ciba_participant.participant.models",
    "ciba_participant.program.models",
    "ciba_participant.content_library.models",
]


@pytest.fixture(scope="session", autouse=True)
def initialize_test_db(request, event_loop):
    config = getDBConfig(
        modules=MODULES,
        app_label="models",
    )

    event_loop.run_until_complete(_init_db(config))

    def shutdown():
        event_loop.run_until_complete(Tortoise._drop_databases())

    request.addfinalizer(shutdown)
