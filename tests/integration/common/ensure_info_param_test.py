import strawberry

from src.common.utils import ensure_info_param


def test_ensure_info_param():
    """
    using info without type hint strawberry.Info is deprecated
    """

    def test_func(info, param):
        return param

    info = ensure_info_param(test_func)

    assert info.already_existed is True
    assert info.name == "info"


def test_ensure_info_param_not_exists():
    def test_func(param):
        return param

    info = ensure_info_param(test_func)

    assert info.already_existed is False

    # This is the given name when the parameter does not exist
    assert info.name == "_info"


def test_ensure_info_param_exists_typed_with_info():
    def test_func(param, info: strawberry.Info):
        return param

    info = ensure_info_param(test_func)

    assert info.already_existed is True
    assert info.name == "info"


def test_ensure_info_param_exists_typed_with_info_with_other_name():
    def test_func(param, other_name: strawberry.Info):
        return param

    info = ensure_info_param(test_func)

    assert info.already_existed is True
    assert info.name == "other_name"
