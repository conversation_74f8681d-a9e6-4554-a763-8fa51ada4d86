import secrets
import string

import httpx
import jwt.algorithms
from cryptography.hazmat.primitives.asymmetric import rsa

from src.auth.jwks import JWK, JWKS, JWKSCache, get_jwks_cache


def test_get_jwks_cache():
    jwks_url = "https://example.com/.well-known/jwks.json"
    ttl = 60

    jwks_cache_a = get_jwks_cache(jwks_url, ttl, immediate_refresh=False)
    jwks_cache_b = get_jwks_cache(jwks_url, ttl, immediate_refresh=False)

    assert jwks_cache_a is jwks_cache_b

    jwks_cache_c = get_jwks_cache(jwks_url, ttl + 1, immediate_refresh=False)

    assert jwks_cache_a is not jwks_cache_c


def generate_random_string(length=32):
    return "".join(
        secrets.choice(string.ascii_lowercase) for _ in range(length)
    )


def generate_jwks(n=10) -> JWKS:
    keys = []

    for _ in range(n):
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )

        public_key = private_key.public_key()
        kid = generate_random_string()
        public_jwk = jwt.algorithms.RSAAlgorithm.to_jwk(
            public_key, as_dict=True
        )
        jwk = JWK(
            alg="RS256",
            e=public_jwk["e"],
            kid=kid,
            kty="RSA",
            n=public_jwk["n"],
            use="sig",
        )
        keys.append(jwk)

    jwks = JWKS(keys=keys)

    return jwks


def test_jwks_cache():
    jwks = initial_jwks = generate_jwks()

    def handler(request):
        nonlocal jwks
        jwks = generate_jwks()
        return httpx.Response(200, json=jwks.model_dump())

    transport = httpx.MockTransport(handler=handler)

    client = httpx.Client(transport=transport)
    JWKSCache._client = client

    # On creation the cache refreshes immediately
    jwks_cache = JWKSCache(
        jwks_url="https://example.com/.well-known/jwks.json", ttl=60
    )
    assert jwks != initial_jwks

    first_jwk = jwks.keys[0]

    # The key exists so the cache won't be refreshed
    assert jwks_cache.get_key(first_jwk.kid) == first_jwk
    assert not jwks_cache._just_refreshed

    key = jwks_cache.get_key("non-existent-kid")
    assert key is None
    # The key doesn't exist so the cache refreshes
    assert jwks_cache._just_refreshed
    assert jwks_cache.get_key(first_jwk.kid) is None
