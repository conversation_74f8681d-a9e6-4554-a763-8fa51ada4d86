import pytest
from ciba_participant.test_utils import (
    create_authorized_user,
    create_participant,
    clear_db,
    create_webinar,
    create_live_session,
)

from src.common.input import PaginationInput
from src.webinar.queries import get_webinars
from src.webinar.types import WebinarListType


@pytest.fixture(scope="function")
async def happy_path():
    await clear_db()

    authorized_user = await create_authorized_user()
    (
        participant,
        solera_participant,
        participant_meta,
    ) = await create_participant()

    webinar = await create_webinar(host_id=authorized_user.id)
    live_session = await create_live_session(webinar_id=webinar.id)

    return authorized_user, webinar, live_session


@pytest.mark.asyncio
async def test_live_session_recording_full_flow(happy_path):
    authorized_user, webinar, live_session = await happy_path

    result = await get_webinars(
        PaginationInput(page=1, per_page=10),
    )

    # Verify result format
    assert isinstance(result, WebinarListType)
    assert len(result.webinars) == 1
    assert result.total_pages == 1

    # Verify webinar data is formatted correctly
    formatted_webinar = result.webinars[0]
    assert formatted_webinar.id == webinar.id
    assert formatted_webinar.topic == webinar.topic
    assert formatted_webinar.title == webinar.title
    assert formatted_webinar.description == webinar.description
    assert len(formatted_webinar.live_sessions) == 1
    assert formatted_webinar.live_sessions[0].id == live_session.id
    assert formatted_webinar.featured_session.id == live_session.id
    assert formatted_webinar.host.id == authorized_user.id
