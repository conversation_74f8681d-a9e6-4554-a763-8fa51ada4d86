# TODO: Rewrite with new recording flow
# from datetime import datetime
#
# import pytest
# from unittest.mock import AsyncMock, patch
#
# from ciba_participant.classes.models import LiveSession
# from ciba_participant.cohort.models import CohortProgramModules
# from ciba_participant.test_utils import (
#     DateWhen,
#     add_participant_to_cohort,
#     create_authorized_user,
#     create_cohort,
#     create_full_program,
#     create_participant,
#     create_participant_activity,
#     create_participant_weight_activity,
#     clear_db,
# )
#
# from src.cohort.mutations import upload_video_recording
# from src.cohort.mutations import update_live_session_recording
# from src.cohort.mutations import delete_live_session_recording
#
#
# @pytest.fixture(scope="function")
# async def happy_path():
#     with patch(
#         "ciba_participant.schedule_manager.service.ScheduleManager.create_zoom_meetings",
#         new_callable=AsyncMock,
#     ):
#         await clear_db()
#
#         program, module, section = await create_full_program()
#         authorized_user = await create_authorized_user()
#         (
#             participant,
#             solera_participant,
#             participant_meta,
#         ) = await create_participant()
#
#         cohort = await create_cohort(
#             program_id=program.id,
#             created_by=authorized_user.id,
#             cohort_date=DateWhen.NOW,
#         )
#
#         await add_participant_to_cohort(
#             cohort_id=cohort.id, participant_id=participant.id
#         )
#
#         await create_participant_activity(participant.id)
#         await create_participant_weight_activity(participant.id)
#
#         return participant, cohort, module
#
#
# @pytest.mark.asyncio
# async def test_live_session_recording_full_flow(happy_path):
#     participant, cohort, modules = await happy_path
#
#     cohort_module = await CohortProgramModules.filter(
#         cohort_id=cohort.id, program_module_id=modules.id
#     ).first()
#
#     live_session = await LiveSession.create(
#         cohort_id=cohort.id,
#         cohort_program_module_id=cohort_module.id,
#         zoom_id="123456789",
#         zoom_meeting_id="test_zoom_meeting_id",
#         zoom_meeting_password="test_zoom_meeting_password",
#         meeting_start_time=datetime.now(),
#     )
#
#     recording_url = "https://www.youtube.com/watch?v=test_url"
#     status = await upload_video_recording(
#         live_session_id=live_session.id,
#         video_url=recording_url,
#     )
#
#     updated_live_session = await LiveSession.get(id=live_session.id)
#
#     assert status.status is True
#     assert updated_live_session.recording_url == recording_url
#
#     recording_url = "https://www.youtube.com/watch?v=test_url_2"
#
#     status = await update_live_session_recording(
#         live_session_id=live_session.id,
#         video_url=recording_url,
#     )
#
#     updated_live_session = await LiveSession.get(id=live_session.id)
#
#     assert status.status is True
#     assert updated_live_session.recording_url == recording_url
#
#     status = await delete_live_session_recording(
#         live_session_id=live_session.id,
#     )
#
#     updated_live_session = await LiveSession.get(id=live_session.id)
#
#     assert status.status is True
#     assert updated_live_session.recording_url is None
