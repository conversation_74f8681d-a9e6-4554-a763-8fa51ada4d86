from unittest.mock import patch, MagicMock
from uuid import uuid4

import pendulum
import pytest
from tortoise.exceptions import BaseORMException

from ciba_participant.classes.models import (
    RawLiveSession,
    MeetingTypeEnum,
    TopicEnum,
)
from src.content_library.messages import DB_READ_ERROR
from src.schema import schema

test_query = """
    query GetLiveSessions($startDate: DateTime!, $endDate: DateTime!, $timezone: String) {
      getLiveSessions(startDate: $startDate, endDate: $endDate, timezone: $timezone) {
        liveSessions {
          id
          bookingsCount
          maxCapacity
          webinarId
        }
        success
        error
      }
    }
"""
test_variables = {
    "startDate": "2025-04-01T00:00:00",
    "endDate": "2025-05-01T00:00:00",
    "timezone": "America/New_York",
}
test_error = "Invalid live session query"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_exception, expected_message",
    [
        (ValueError(test_error), test_error),
        (BaseORMException(), DB_READ_ERROR),
    ],
)
async def test_get_live_sessions_with_exceptions(
    test_exception, expected_message, mock_client_context
):
    """
    get_live_sessions should return an empty list with unsuccessful status
    when an exception is raised.
    """
    with patch(
        "ciba_participant.classes.crud.LiveSessionRepository.get_live_sessions",
        side_effect=test_exception,
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getLiveSessions"]["liveSessions"] == []
        assert actual_value.data["getLiveSessions"]["success"] is False
        assert (
            actual_value.data["getLiveSessions"]["error"] == expected_message
        )


@pytest.mark.asyncio
async def test_get_live_sessions_success(mock_client_context):
    """
    get_live_sessions should return a list of live sessions without errors.
    """
    test_id = uuid4()
    test_date = pendulum.parse("2020-01-31")
    test_url = "https://mock.com"

    test_session = MagicMock(spec=RawLiveSession)
    test_session.id = test_id
    test_session.created_at = test_date
    test_session.updated_at = test_date
    test_session.title = "My Title"
    test_session.description = "My Description"
    test_session.meeting_start_time = test_date
    test_session.timezone = "UTC"
    test_session.has_conflict = False
    test_session.meeting_type = MeetingTypeEnum.INSTANT
    test_session.webinar_id = test_id
    test_session.host_id = test_id
    test_session.topic = TopicEnum.FOOD
    test_session.recording_url = test_url
    test_session.zoom_id = "zoom1"
    test_session.zoom_occurrence_id = "zoom2"
    test_session.zoom_link = test_url
    test_session.use_custom_meeting_link = False
    test_session.custom_meeting_link = None
    test_session.max_capacity = 10
    test_session.bookings_count = 1
    test_session.webinar_id = test_id

    with patch(
        "ciba_participant.classes.crud.LiveSessionRepository.get_live_sessions",
        return_value=[test_session],
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getLiveSessions"]["liveSessions"] == [
            {
                "id": str(test_id),
                "bookingsCount": 1,
                "maxCapacity": 10,
                "webinarId": str(test_id),
            }
        ]
        assert actual_value.data["getLiveSessions"]["success"] is True
        assert actual_value.data["getLiveSessions"]["error"] is None
