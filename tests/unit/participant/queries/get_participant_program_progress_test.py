import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4
from datetime import datetime, timedelta

from src.schema import schema
from tortoise.exceptions import BaseORMException
from src.program.types import ProgramModuleType


test_query = """
    query GetParticipantProgramProgress($participantId: UUID!, $cohortId: UUID!) {
      getParticipantProgramProgress(participantId: $participantId, cohortId: $cohortId) {
        programModule {
          id
          title
          description
        }
        totalActivities
        completedActivities
      }
    }
"""

test_variables = {"participantId": str(uuid4()), "cohortId": str(uuid4())}


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_exception, expected_result",
    [
        (ValueError("Invalid program progress query"), None),
        (BaseORMException(), None),
    ],
)
async def test_get_participant_program_progress_with_exceptions(
    test_exception, expected_result, mock_client_context
):
    """
    getParticipantProgramProgress should return null when an exception is raised.
    """
    # Patch the database query to avoid actual DB calls
    with patch(
        "ciba_participant.cohort.models.CohortProgramModules.filter",
        side_effect=test_exception,
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert (
            actual_value.data["getParticipantProgramProgress"]
            == expected_result
        )


@pytest.fixture
def mock_program_progress():
    """Fixture to create a consistent mock program progress object"""
    # Create a mock program module
    mock_module = MagicMock()
    mock_module.id = uuid4()
    mock_module.title = "Test Module"
    mock_module.description = "Test Description"
    mock_module.created_at = datetime.now()
    mock_module.updated_at = datetime.now()
    mock_module.short_title = "Test"
    mock_module.length = 4
    mock_module.program_id = uuid4()
    mock_module.order = 1
    mock_module.started_at = datetime.now()
    mock_module.ended_at = datetime.now() + timedelta(weeks=4)
    mock_module.cohort_module_id = uuid4()

    # Create a mock program module type
    program_module_type = ProgramModuleType(
        id=mock_module.id,
        created_at=mock_module.created_at,
        updated_at=mock_module.updated_at,
        title=mock_module.title,
        short_title=mock_module.short_title,
        length=mock_module.length,
        description=mock_module.description,
        program_id=mock_module.program_id,
        order=mock_module.order,
        started_at=mock_module.started_at,
        ended_at=mock_module.ended_at,
        cohort_module_id=mock_module.cohort_module_id,
    )

    # Create a list of mock progress items
    mock_progress = [
        {
            "program_module": program_module_type,
            "total_activities": 10,
            "completed_activities": 5,
        }
    ]

    return mock_progress


@pytest.mark.asyncio
async def test_get_participant_program_progress_success(
    mock_client_context, mock_program_progress
):
    """
    getParticipantProgramProgress should return program progress data without errors.
    """
    # Mock the GraphQL execution result directly
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantProgramProgress": [
            {
                "programModule": {
                    "id": str(mock_program_progress[0]["program_module"].id),
                    "title": mock_program_progress[0]["program_module"].title,
                    "description": mock_program_progress[0][
                        "program_module"
                    ].description,
                },
                "totalActivities": mock_program_progress[0][
                    "total_activities"
                ],
                "completedActivities": mock_program_progress[0][
                    "completed_activities"
                ],
            }
        ]
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "cohortId": str(uuid4()),
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantProgramProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert len(result) == 1

        # Validate program module
        assert result[0]["programModule"]["id"] == str(
            mock_program_progress[0]["program_module"].id
        )
        assert (
            result[0]["programModule"]["title"]
            == mock_program_progress[0]["program_module"].title
        )
        assert (
            result[0]["programModule"]["description"]
            == mock_program_progress[0]["program_module"].description
        )

        # Validate activities
        assert (
            result[0]["totalActivities"]
            == mock_program_progress[0]["total_activities"]
        )
        assert (
            result[0]["completedActivities"]
            == mock_program_progress[0]["completed_activities"]
        )
