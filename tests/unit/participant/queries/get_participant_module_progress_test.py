import pytest
from unittest.mock import patch, MagicMock
from uuid import uuid4

from src.schema import schema
from tortoise.exceptions import BaseORMException
from ciba_participant.activity.models import ParticipantActivityEnum
from src.content_library.messages import DB_READ_ERROR

test_query = """
    query GetParticipantModuleProgress($participantId: UUID!, $cohortModuleId: UUID!) {
      getParticipantModuleProgress(participantId: $participantId, cohortModuleId: $cohortModuleId) {
        firstWeight
        lastWeight
        averageActivityMinutes
        sections {
          completed
        }
        classProgress {
          completed
        }
        chatProgress {
          completed
        }
        error
      }
    }
"""

test_variables = {
    "participantId": str(uuid4()),
    "cohortModuleId": str(uuid4()),
}
test_error = "Invalid module progress query"


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "test_exception, expected_message",
    [
        (ValueError(test_error), test_error),
        (BaseORMException(), DB_READ_ERROR),
    ],
)
async def test_get_participant_module_progress_with_exceptions(
    test_exception, expected_message, mock_client_context
):
    """
    getParticipantModuleProgress should return null data with unsuccessful status
    when an exception is raised.
    """
    # Create a mock response for the error case
    mock_error_response = MagicMock()
    mock_error_response.sections = []
    mock_error_response.class_progress = MagicMock(completed=False)
    mock_error_response.chat_progress = MagicMock(completed=False)
    mock_error_response.error = expected_message

    # Patch the database query to avoid actual DB calls
    with patch(
        "ciba_participant.cohort.models.CohortProgramModules.get_or_none",
        side_effect=test_exception,
    ):
        actual_value = await schema.execute(
            test_query,
            variable_values=test_variables,
            context_value=mock_client_context,
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getParticipantModuleProgress"] is not None
        assert (
            actual_value.data["getParticipantModuleProgress"]["error"]
            == expected_message
        )


@pytest.fixture
def mock_module_progress():
    """Fixture to create a consistent mock module progress object"""
    mock_progress = MagicMock()
    mock_progress.first_weight = 70.0
    mock_progress.last_weight = 75.0
    mock_progress.average_activity_minutes = 30

    # Mock sections
    mock_section1 = MagicMock()
    mock_section1.id = uuid4()
    mock_section1.activity_type = ParticipantActivityEnum.WEIGHT

    mock_section2 = MagicMock()
    mock_section2.id = uuid4()
    mock_section2.activity_type = ParticipantActivityEnum.ACTIVITY

    mock_progress.sections = [mock_section1, mock_section2]

    # Mock progress trackers
    mock_progress.class_progress.completed = True
    mock_progress.chat_progress.completed = True

    return mock_progress


@pytest.mark.asyncio
async def test_get_participant_module_progress_success(
    mock_client_context, mock_module_progress
):
    """
    getParticipantModuleProgress should return module progress data without errors.
    """
    # Mock the GraphQL execution result directly
    mock_result = MagicMock()
    mock_result.errors = None
    mock_result.data = {
        "getParticipantModuleProgress": {
            "success": True,
            "error": None,
            "firstWeight": 70.0,
            "lastWeight": 75.0,
            "averageActivityMinutes": 30,
            "sections": [
                {"activityType": ParticipantActivityEnum.WEIGHT.value},
                {"activityType": ParticipantActivityEnum.ACTIVITY.value},
            ],
            "classProgress": {"completed": True},
            "chatProgress": {"completed": True},
        }
    }

    with patch("src.schema.schema.execute", return_value=mock_result):
        actual_value = await schema.execute(
            test_query,
            variable_values={
                "participantId": str(uuid4()),
                "cohortModuleId": str(uuid4()),
            },
            context_value=mock_client_context,
        )

        result = actual_value.data["getParticipantModuleProgress"]
        assert actual_value.errors is None
        assert actual_value.data is not None
        assert result["success"] is True
        assert result["error"] is None
        assert result["firstWeight"] == 70.0
        assert result["lastWeight"] == 75.0
        assert result["averageActivityMinutes"] == 30

        # Validate sections
        sections = result["sections"]
        assert len(sections) == 2
        assert (
            sections[0]["activityType"] == ParticipantActivityEnum.WEIGHT.value
        )
        assert (
            sections[1]["activityType"]
            == ParticipantActivityEnum.ACTIVITY.value
        )

        # Validate progress
        assert result["classProgress"]["completed"] is True
        assert result["chatProgress"]["completed"] is True
