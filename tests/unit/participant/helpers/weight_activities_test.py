from datetime import date

import pendulum
import pytest

from ciba_participant.activity.models import (
    ParticipantActivity,
    ParticipantActivityDevice,
)
from src.participant.helpers.weight_activities import (
    get_weight_history_activities,
)


@pytest.fixture
def mock_weight_activities():
    test_values = [
        {
            "date": pendulum.parse("2000-01-01T00:30:00"),
            "value": 200,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-01T08:30:00"),
            "value": 10,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-01T16:30:00"),
            "value": None,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-02T00:30:00"),
            "value": 198.5,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-02T08:30:00"),
            "value": 198.6,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-02T16:30:00"),
            "value": 198.4,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-03T00:30:00"),
            "value": 5,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-03T08:30:00"),
            "value": 198.2,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
        {
            "date": pendulum.parse("2000-01-03T16:30:00"),
            "value": 9.9,
            "activity_device": ParticipantActivityDevice.WITHINGS,
        },
    ]

    mocked_weight_activities = []

    for test_value in test_values:
        mocked_weight_activities.append(
            ParticipantActivity(
                created_at=test_value["date"],
                value=test_value["value"],
                activity_device=test_value["activity_device"],
            )
        )

    return mocked_weight_activities


def test_get_weight_history_activities_with_daily_resampling_success(
    mock_weight_activities,
):
    """
    get_weight_history_activities should return a list of daily weight activities
    filtering values below less than 10 when using daily resampling
    """
    actual_value = get_weight_history_activities(
        mock_weight_activities, index_column="date", resample_freq="d"
    )

    assert len(actual_value) == 3
    assert actual_value[0].weight == 200
    assert actual_value[0].date.date() == date(2000, 1, 1)
    assert actual_value[0].source == "withings"
    assert actual_value[1].weight == 198.4
    assert actual_value[1].date.date() == date(2000, 1, 2)
    assert actual_value[1].source == "withings"
    assert actual_value[2].weight == 198.2
    assert actual_value[2].date.date() == date(2000, 1, 3)
    assert actual_value[2].source == "withings"


@pytest.mark.parametrize(
    "test_value",
    [
        [
            ParticipantActivity(
                created_at=pendulum.parse("2000-01-01T00:00:00"),
                value=5,
                activity_device=ParticipantActivityDevice.WITHINGS,
            ),
            ParticipantActivity(
                created_at=pendulum.parse("2000-01-02T00:00:00"),
                value=None,
                activity_device=ParticipantActivityDevice.WITHINGS,
            ),
        ],
        [],
    ],
)
def test_get_weight_history_activities_with_empty_result(test_value):
    """
    get_weight_history_activities should return an empty list
    when no activities were provided
    or when no activities remain after filtering
    """
    actual_value = get_weight_history_activities(test_value)

    assert actual_value == []
