from unittest.mock import AsyncMock, MagicMock, patch
import pendulum
import pytest

from ciba_participant.classes.crud import LiveSessionRepository, WebinarRepository
from ciba_participant.classes.errors import LiveSessionError
from ciba_participant.classes.models import Booking, TopicEnum
from ciba_participant.common.aws_handler import (
    EmailNotificationEvent,
    NotificationType,
    SQSNotification,
)
from ciba_participant.common.converters import from_utc_to_pst
from ciba_participant.error_messages.classes import ERROR_TIME_LIMIT_FOR_DELETE
from ciba_participant.test_utils import (
    DateWhen,
    create_authorized_user,
    create_booking,
    create_full_program,
    create_cohort,
    create_live_session,
    create_participant,
    add_participant_to_cohort,
    create_webinar,
)


@pytest.fixture()
async def scenario_1():
    cohort_start: DateWhen = DateWhen.FUTURE
    authorized_user = await create_authorized_user()
    program_resp, _, _ = await create_full_program()

    cohort = await create_cohort(
        program_id=program_resp.id,
        created_by=authorized_user.id,
        cohort_date=cohort_start,
    )

    participant, _, _ = await create_participant()
    await add_participant_to_cohort(cohort_id=cohort.id, participant_id=participant.id)

    webinar = await create_webinar(
        host_id=authorized_user.id, topic=TopicEnum.INTRO_SESSION
    )

    meeting_start = pendulum.instance(cohort.started_at).add(days=5)

    live_session = await create_live_session(
        webinar_id=webinar.id, meeting_start_time=meeting_start
    )

    yield participant, webinar, live_session


@pytest.fixture()
async def scenario_2():
    cohort_start: DateWhen = DateWhen.PAST
    authorized_user = await create_authorized_user()
    program_resp, _, _ = await create_full_program()

    cohort = await create_cohort(
        program_id=program_resp.id,
        created_by=authorized_user.id,
        cohort_date=cohort_start,
    )

    participant, _, _ = await create_participant()
    await add_participant_to_cohort(cohort_id=cohort.id, participant_id=participant.id)

    webinar = await create_webinar(
        host_id=authorized_user.id, topic=TopicEnum.INTRO_SESSION
    )

    meeting_start = pendulum.now().add(hours=2)

    meeting_start = pendulum.instance(cohort.started_at).subtract(days=5)

    live_session = await create_live_session(
        webinar_id=webinar.id, meeting_start_time=meeting_start
    )

    yield participant, webinar, live_session


@pytest.mark.asyncio
async def test_delete_empty_live_session(scenario_1):
    """Scenario 1: session is deleted correctly"""
    _, webinar, live_session = scenario_1

    mock_schedule_mgr = MagicMock()
    mock_del_meeting = AsyncMock()
    mock_del_occurrence = AsyncMock()

    mock_schedule_mgr.delete_zoom_meeting_occurrence = mock_del_occurrence
    mock_schedule_mgr.delete_zoom_meeting = mock_del_meeting

    original_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)

    with patch(
        "ciba_participant.classes.crud.live_sessions.ScheduleManager",
        return_value=mock_schedule_mgr,
    ):
        await LiveSessionRepository.delete_live_session(live_session.id)

    updated_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)

    assert (
        len(updated_webinar.sessions or []) == len(original_webinar.sessions or []) - 1
    )
    mock_del_occurrence.assert_called_once()
    mock_del_meeting.assert_not_called()


@pytest.mark.asyncio
async def test_delete_live_session_with_booking(scenario_1):
    """Scenario 2: session is deleted correctly even with a booking"""
    participant, webinar, live_session = scenario_1

    booking = await create_booking(participant.id, live_session.id)

    mock_schedule_mgr = MagicMock()
    mock_del_meeting = AsyncMock()
    mock_del_occurrence = AsyncMock()

    mock_schedule_mgr.delete_zoom_meeting_occurrence = mock_del_occurrence
    mock_schedule_mgr.delete_zoom_meeting = mock_del_meeting

    original_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)

    with patch(
        "ciba_participant.classes.crud.live_sessions.ScheduleManager",
        return_value=mock_schedule_mgr,
    ):
        with patch(
            "ciba_participant.classes.crud.live_sessions.send_to_sqs"
        ) as mock_send_to_sqs:
            await LiveSessionRepository.delete_live_session(live_session.id)

            updated_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)
            deleted_booking = await Booking.get_or_none(id=booking.id)

            assert (
                len(updated_webinar.sessions or [])
                == len(original_webinar.sessions or []) - 1
            )
            mock_del_occurrence.assert_called_once()
            mock_del_meeting.assert_not_called()
            assert deleted_booking is None

            class_date = pendulum.instance(live_session.meeting_start_time).astimezone(
                tz=pendulum.timezone(live_session.timezone)
            )

            message = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.CANCELLED_SESSION,
                data={
                    "email": participant.email,
                    "first_name": participant.first_name,
                    "class_name": live_session.title,
                    "class_date": from_utc_to_pst(class_date, "MM/DD/YYYY hh:mm A zz"),
                },
            )

            mock_send_to_sqs.assert_called_once_with(
                queue_url="",
                message_body=message.model_dump_json(),
            )


@pytest.mark.asyncio
async def test_delete_past_live_session(scenario_2):
    """Scenario 3: session isn't deleted, as the meeting time is in the past."""
    _, webinar, live_session = scenario_2

    mock_schedule_mgr = MagicMock()
    mock_del_meeting = AsyncMock()
    mock_del_occurrence = AsyncMock()

    mock_schedule_mgr.delete_zoom_meeting_occurrence = mock_del_occurrence
    mock_schedule_mgr.delete_zoom_meeting = mock_del_meeting

    original_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)

    with patch(
        "ciba_participant.classes.crud.live_sessions.ScheduleManager",
        return_value=mock_schedule_mgr,
    ):
        with pytest.raises(LiveSessionError, match=ERROR_TIME_LIMIT_FOR_DELETE):
            await LiveSessionRepository.delete_live_session(live_session.id)

    updated_webinar = await WebinarRepository.get_webinar(webinar_id=webinar.id)

    assert len(updated_webinar.sessions or []) == len(original_webinar.sessions or [])
    mock_del_occurrence.assert_not_called()
    mock_del_meeting.assert_not_called()
