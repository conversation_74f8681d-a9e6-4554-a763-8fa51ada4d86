from unittest.mock import MagicMock, patch

import pendulum
import pytest

from src.content_library.enums import MaterialStatusEnum
from src.schema import schema
from tests.unit.content_library.common import test_material_id

test_date = pendulum.parse("1999-07-04")
test_link = "https://mock.com"
test_query = """
    query GetContentMaterial {
      getContentMaterial {
        success
        error
        items {
          id
          link
          linkExpiration
        }
        total
        totalPages
      }
    }
"""


@pytest.fixture
def mock_material_list():
    """
    fixture that mocks a material query response from ciba_participant.
    """
    test_material = MagicMock()
    test_material.id = test_material_id
    test_material.created_at = test_date
    test_material.mime_type = "text/plain"
    test_material.title = "Mocked material"
    test_material.description = "Mocked description"
    test_material.status = MaterialStatusEnum.ACTIVE
    test_material.link = test_link
    test_material.file_url_expiration = test_date
    test_material.tags = []
    test_material.activity_types = []

    mock_response = MagicMock()
    mock_response.total = 1
    mock_response.total_pages = 1
    mock_response.items = [test_material]

    return mock_response


@pytest.mark.asyncio
async def test_get_content_material_success(
    mock_client_context, mock_material_list
):
    """
    get_content_material should return a list of materials
    """
    with (
        patch(
            "ciba_participant.content_library.crud.ContentMaterialRepository.get_material",
            return_value=mock_material_list,
        ),
        patch(
            "ciba_participant.program.crud.ProgramRepository.get_all_programs_names_map",
            return_value={},
        ),
    ):
        actual_value = await schema.execute(
            test_query, context_value=mock_client_context
        )

        assert actual_value.errors is None
        assert actual_value.data is not None
        assert actual_value.data["getContentMaterial"]["success"] is True
        assert actual_value.data["getContentMaterial"]["items"] == [
            {
                "id": str(test_material_id),
                "link": test_link,
                "linkExpiration": test_date.isoformat(),
            }
        ]
