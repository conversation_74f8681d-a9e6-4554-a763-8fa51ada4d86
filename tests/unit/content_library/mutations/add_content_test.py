from unittest.mock import MagicMock

import pytest

from src.content_library.inputs import MaterialData
from src.content_library.messages import (
    TITLE_EMPTY,
    DESCRIPTION_EMPTY,
    TITLE_TOO_LONG,
    DESCRIPTION_TOO_LONG,
)
from src.content_library.mutations.add_content import (
    validate_title_and_description,
)


@pytest.fixture(
    params=[
        (None, "description", TITLE_EMPTY),
        ("", "description", TITLE_EMPTY),
        ("title", None, DESCRIPTION_EMPTY),
        ("title", "", DESCRIPTION_EMPTY),
        ("X" * 256, "short description", TITLE_TOO_LONG),
        ("title", "X" * 501, DESCRIPTION_TOO_LONG),
    ]
)
def mock_invalid_title_and_description_data(request):
    """
    parametrized fixture to prepare data for title and description
    failed validation test.
    """
    test_title, test_description, error = request.param
    test_data = MagicMock(spec=MaterialData)
    test_data.title = test_title
    test_data.description = test_description

    return test_data, error


def test_validate_title_and_description_success():
    """
    validate_title_and_description should be executed without errors.
    """
    test_data = MagicMock(spec=MaterialData)
    test_data.title = "X" * 255
    test_data.description = "X" * 500

    validate_title_and_description(test_data)


def test_validate_title_and_description_with_empty_values(
    mock_invalid_title_and_description_data,
):
    """
    validate_title_and_description should raise an error
    when a title or description is empty or has too many characters.
    """
    test_data, expected_error = mock_invalid_title_and_description_data

    with pytest.raises(ValueError) as error_info:
        validate_title_and_description(test_data)

    assert str(error_info.value) == expected_error
