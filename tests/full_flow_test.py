# import uuid
#
# from src.cohort.mutations.create_cohort import CreateCohortInput
# from src.participant.queries import get_participant, get_authorized
# from src.participant.types import AuthorizedType, ParticipantType
# from src.program.mutations import (
#     create_program,
#     create_program_module,
#     create_program_module_section
# )
# from src.cohort.mutations import create_cohort, create_cohort_member
# from src.cohort.inputs import CohortMemberCreateInput, RecurrentDatesInput
# from src.program.queries import get_program
# from src.program.inputs import (
#     ProgramCreateInput,
#     ProgramModuleCreateInput,
#     ProgramModuleSectionCreateInput,
#     ProgramModuleSectionMetadataInput,
#     GetProgramInput
# )
# from ciba_participant.activity.models import ParticipantActivityEnum, ParticipantActivityCategory, \
#     ParticipantActivityDevice, ActivityUnit
# from src.participant.mutations import create_participant, create_solera_participant, create_participant_meta, \
#     create_authorized
# from src.participant.inputs import ParticipantCreateInput, ParticipantMetaCreateInput, SoleraParticipantCreateInput, \
#     AuthorizedCreateInput, ParticipantIDInput
# from src.activity.mutations import create_participant_activity
# from src.activity.inputs import ParticipantActivityInput
# from ciba_participant.participant.models import ParticipantStatus, AutorizedRole
# import pytest
# from tortoise import Tortoise
# from ciba_participant.db import get_tortoise_orm_config
# from ciba_participant.settings import get_settings
# import random
# from uuid import UUID
# import pendulum
# import mimesis
# from src.log.logging import logger
#
# settings = get_settings()
# TORTOISE_ORM = get_tortoise_orm_config(settings.default_db_url)
#
#
# async def init_db():
#     await Tortoise.init(TORTOISE_ORM)
#     await Tortoise.generate_schemas()
#
#
# async def close_db():
#     await Tortoise.close_connections()
#
#
# async def create_authorized_users():
#     authorized = {}
#     for role in list(AutorizedRole):
#         person = mimesis.Person()
#         resp = await create_authorized(
#             AuthorizedCreateInput(
#                 email=person.email(),
#                 first_name=person.first_name(),
#                 last_name=person.last_name(),
#                 role=role
#             )
#         )
#         logger.info(f"Created authorized user {resp.id} with role {role}")
#         authorized[role] = resp.id
#     return authorized
#
#
# async def create_modules(modules_number: int, program_id: UUID):
#     module_ids = []
#     for i in range(modules_number):
#         logger.info(f"Creating module {i} for program {program_id}")
#         program_module_data = ProgramModuleCreateInput(
#             title=f"Test Module {i}",
#             short_title=f"Module {i}",
#             length=random.randint(5, 21),
#             description=f"Test Module Description {i}",
#             program_id=program_id,
#             order=i
#         )
#         module_resp = await create_program_module(info=None, data=program_module_data)
#         module_ids.append(module_resp.id)
#     return module_ids
#
#
# async def create_sections(sections_number: int, module_id: UUID, random_datetimes_list: list):
#     section_ids = []
#
#     for i in range(sections_number):
#         logger.info(f"Creating section {i} for module {module_id}")
#         program_module_section_data = ProgramModuleSectionCreateInput(
#             title=f"Test Section {i}",
#             description=f"Test Section Description {i}",
#             program_module_id=module_id,
#             activity_type=random.choice(list(ParticipantActivityEnum)),
#             activity_category=random.choice(list(ParticipantActivityCategory)),
#             metadata=ProgramModuleSectionMetadataInput(
#                 started_at=random.choice(random_datetimes_list),
#                 url=random.choice(["https://www.google.com", ""]),
#                 form_id=str(random.randint(1000, 9999)),
#             )
#         )
#         section_resp = await create_program_module_section(info=None, data=program_module_section_data)
#         section_ids.append(section_resp)
#     return section_ids
#
#
# async def create_cohorts(cohorts_number: int, program_id: UUID, created_by: UUID):
#     cohort_ids = []
#
#     for i in range(cohorts_number):
#         logger.info(f"Creating cohort {i} for program {program_id}")
#         now = pendulum.now()
#         dates = [now.add(days=days_n) for days_n in range(3, 100, 2)]
#
#         cohort_resp = await create_cohort(
#             data=CreateCohortInput(
#                 name=f"Test Cohort {pendulum.now().isoformat()}",
#                 program_id=program_id,
#                 limit=random.randint(0, 100),
#                 start_date=random.choice(dates),
#                 first_meeting_title=f"Test Meeting {i}",
#                 first_meeting_description=f"Test Meeting Description {i}",
#                 live_sessions=RecurrentDatesInput(
#                     count=1,
#                     frequency=random.choice(["DAILY", "WEEKLY", "MONTHLY"]),
#                     interval=random.randint(1, 10),
#                     starting_from=pendulum.now(),
#                     timezone="UTC"
#                 )
#             ),
#             created_by=created_by
#         )
#         cohort_ids.append((cohort_resp.id, cohort_resp.limit))
#     return cohort_ids
#
#
# async def create_participants(participants_number: int):
#     participant_ids = []
#
#     for i in range(participants_number):
#         person = mimesis.Person()
#         logger.info(f"Creating participant {i} with email {person.email()}")
#         participant_resp = await create_participant(
#             ParticipantCreateInput(
#                 first_name=person.first_name(),
#                 last_name=person.last_name(),
#                 email=person.email(),
#                 group_id=uuid.uuid4(),
#                 member_id=uuid.uuid4(),
#                 status=random.choice(list(ParticipantStatus)),
#                 is_test=False,
#                 last_reset=pendulum.now().isoformat(),
#                 medical_record=person.username(),
#                 cognito_sub=uuid.uuid4()
#             )
#         )
#         participant_ids.append(participant_resp.id)
#         solera_participant_resp = await create_solera_participant(
#             SoleraParticipantCreateInput(
#                 participant_id=participant_resp.id,
#                 solera_id=uuid.uuid4(),
#                 solera_key=person.username(),
#                 solera_program_id=person.username(),
#                 solera_enrollment_id=person.username()
#             )
#         )
#         meta_resp = await create_participant_meta(
#             ParticipantMetaCreateInput(
#                 participant_id=participant_resp.id,
#                 metadata="{}"
#
#             )
#         )
#     return participant_ids
#
#
# async def create_participants_activities(particiapnt_id_list: list):
#     for participant_id in particiapnt_id_list:
#         for _ in range(random.randint(1, 10)):
#             logger.info(f"Creating activity for participant {participant_id}")
#             await create_participant_activity(
#                 info=None,
#                 data=ParticipantActivityInput(
#                     participant_id=participant_id,
#                     value=random.randint(1, 100),
#                     unit=random.choice(list(ActivityUnit)),
#                     activity_device=random.choice(list(ParticipantActivityDevice)),
#                     activity_category=random.choice(list(ParticipantActivityCategory)),
#                     activity_type=random.choice(list(ParticipantActivityEnum)),
#                 )
#             )
#
#
# @pytest.mark.asyncio
# async def test_create_full_program():
#     logger.info("Starting full flow test")
#     now = pendulum.now()
#     random_future_datetimes_list = [
#         now.add(days=random.randint(1, 365)).isoformat() for _ in range(random.randint(1, 5))
#     ]
#     random_past_datetimes_list = [
#         now.subtract(days=random.randint(1, 365)).isoformat() for _ in range(random.randint(1, 5))
#     ]
#     random_datetimes_list = random_future_datetimes_list + random_past_datetimes_list
#     random_datetimes_list.append(now.isoformat())
#
#     await init_db()
#     logger.info("DB initialized")
#
#     authorized_map = await create_authorized_users()
#
#     get_authorized_resp = await get_authorized(
#         data=ParticipantIDInput(id=authorized_map[AutorizedRole.ADMIN])
#     )
#     assert get_authorized_resp.id == authorized_map[AutorizedRole.ADMIN]
#     assert isinstance(get_authorized_resp.chat_identity, str)
#
#     program_data = ProgramCreateInput(
#         title=f"Test Program {pendulum.now().isoformat()}",
#         description=f"Test Program Description {pendulum.now().isoformat()}"
#     )
#
#     create_program_reps = await create_program(program_data)
#     logger.info(f"Created program {create_program_reps.id}")
#     program_id = create_program_reps.id
#
#     program_module_ids = await create_modules(
#         modules_number=random.randint(5, 55),
#         program_id=program_id
#     )
#     logger.info(f"Created {len(program_module_ids)} modules for program {program_id}")
#
#     for program_module_id in program_module_ids:
#         await create_sections(
#             sections_number=random.randint(1, 5),
#             module_id=program_module_id,
#             random_datetimes_list=random_datetimes_list
#         )
#
#     logger.info("Created sections for all modules")
#
#     get_program_resp = await get_program(
#         info=None,
#         data=GetProgramInput(id=program_id)
#     )
#
#     assert get_program_resp.id == program_id
#     assert get_program_resp.title == program_data.title
#     assert get_program_resp.description == program_data.description
#     for module in get_program_resp.modules:
#         assert module.program_id == program_id
#         for section in module.sections:
#             assert section.program_module_id == module.id
#     logger.info("Get program response is correct")
#
#     cohort_ids_list = await create_cohorts(
#         cohorts_number=random.randint(1, 20),
#         program_id=program_id,
#         created_by=authorized_map[AutorizedRole.ADMIN]
#     )
#
#     participant_ids_list = await create_participants(
#         participants_number=random.randint(len(cohort_ids_list), len(cohort_ids_list) * 10)
#     )
#     logger.info(f"Created {len(participant_ids_list)} participants")
#     get_participant_resp = await get_participant(ParticipantIDInput(id=participant_ids_list[0]))
#
#     assert get_participant_resp.id == participant_ids_list[0]
#     assert isinstance(get_participant_resp.chat_identity, str)
#
#     await create_participants_activities(participant_ids_list)
#     logger.info("Created activities for all participants")
#
#     for cohort_id, cohort_limit in cohort_ids_list:
#         total_number_of_members = len(participant_ids_list)
#         max_members = random.randint(1, cohort_limit)
#         logger.info(f"Creating {max_members} members for cohort {cohort_id}")
#         # TODO: i need to distibute the participants among the cohorts randomly
#         # but in range of the cohort limit and total number of participants
#         cohort_members = min(max_members, total_number_of_members)
#
#         # for _ in range(cohort_members):
#         #     logger.info(f"Creating member for cohort {cohort_id}")
#         #     create_cohort_member_resp = await create_cohort_member(
#         #         info=None,    # TODO: Add info mocker for this function
#         #         data=CohortMemberCreateInput(
#         #             cohort_id=cohort_id,
#         #             participant_id=random.choice(participant_ids_list)
#         #         )
#         #     )
#         #     participant_ids_list.remove(create_cohort_member_resp.participant_id)
#
#     logger.info("Created members for all cohorts")
#     await close_db()
#     logger.info("DB closed")
