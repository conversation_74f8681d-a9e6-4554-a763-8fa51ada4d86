import asyncio
from typing import Iterator
from unittest.mock import MagicMock
from uuid import uuid4

import pytest


@pytest.fixture(scope="session")
def event_loop() -> Iterator[asyncio.AbstractEventLoop]:
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_client_context():
    test_user = MagicMock()
    test_user.id = uuid4()
    test_user.is_authenticated = True
    test_context = MagicMock()
    test_context.user = test_user

    return test_context
