# Participant Admin API

## Overview

This is the core backend for patient platform administrators, providing a GraphQL API to manage key functionalities such as program and cohort administration, webinar (class) scheduling, and content library material management.

## Tech Stack

This project is built with:

- FastAPI
- Strawberry

For data persistence and third party integrations it depends on the [ciba-participant](https://github.com/Cibahealth/ciba-participant) package.

## Prerequisites

This project requires the following tools.

- [git](https://git-scm.com/) for version control
- [python](https://www.python.org/downloads/) on its 3.12 version
- [uv](https://docs.astral.sh/uv/getting-started/installation/) for dependencies management

## Installation

Steps to configure the project.

### Clone repository

```shell
<NAME_EMAIL>:Cibahealth/participant-admin.git
```

### Install dependencies

To install the main dependencies, inside the project directory run this command.

```shell
uv sync
```

To install all the dependencies, inside the project directory run this command.

```shell
uv sync --all-extras
```

This command automatically creates a virtual environment at the root of the project. Please use this virtual environment to run all the project tools.

## Configuration

Before running the project please take into account the following settings.

### Configure env variables

This project has default settings in the [settings.py](https://github.com/Cibahealth/participant-admin/blob/main/src/settings.py) file.

If you need to override one or more values, this can be done through environment variables.

For Visual Studio Code users, you can create a .env file in the project root with the variables you want to override. This file is referenced in the [launch.json](https://github.com/Cibahealth/participant-admin/blob/main/.vscode/launch.json) file. For example:

```text
POSTGRES_PORT=8081
POSTGRES_DB=local_participant
```

For PyCharm users, you can create a run configuration and in the environment variables section configure the variables you want to override.

![PyCharm Env](https://i.sstatic.net/IstkF.jpg)

### Running the Application

To run the application, please use this command or your IDE run configuration.

```shell
uv run uvicorn src.app:app --reload --host 0.0.0.0 --port 8000
```

Remember to use the project's virtual environment.

### Running local docker image

#### Build participant-api dev image

```shell
make build
```

#### Run docker-compose

```shell
make up
```

#### Run terminal

```shell
make term
```

## Testing

To run test use this command.

```shell
uv run pytest tests/
```

To run tests with coverage report use this command.

```shell
uv run pytest --cov=. tests/
```

To run tests with coverage report in html format use this command.

```shell
uv run pytest --cov=. tests/ --cov-report=html
```

## Deployment

Each merge into the main branch will automatically trigger a deployment to the development environment.

To deploy changes to production, you must follow these steps.

1. Create a new tag following the canary release guidelines
1. Push your tag to the repository `git push origin {{tag_name}}`
1. Create a draft release from the previous tag
1. Generate changelog
1. Ask the contributors and QA for confirmation of the changes to be deployed
1. Publish the release

After the release is published, the production deployment pipeline will run automatically.

## Contributing

To contribute to the project, you must follow the following steps.

1. Create a new branch from the main branch
1. Implement the changes
1. Validate your changes with tests, please follow this [convention](https://ciba-tech-space.notion.site/Python-Test-Conventions-1c8213742f438040acdaf100bd2a4bba)
1. Before to push your changes to the repository please run `uv run pre-commit run --all-files`
1. Fix failing checks if needed
1. Push your branch to the repository and create a pull request
1. Ask your teammates to review your pull request
1. Resolve comments if needed
1. When all the checks passed and the approval was provided, merge the branch and delete it

Please fill out the [template](https://github.com/Cibahealth/participant-admin/blob/main/.github/PULL_REQUEST_TEMPLATE.md) correctly when creating the pull request.

## License

© 2025 Ciba Health All Rights Reserved.
