SHELL := /bin/bash
export IMAGE_PATH := participant-admin-app

build:
	make clean
	DOCKER_BUILDKIT=1 docker build --ssh default -t participant-admin:dev-latest --target dev --build-arg INSTALL_DEV=true .

up:
	docker compose up -d

down:
	docker compose down

clean:
	docker compose down
	docker rmi -f participant-admin_app
	docker volume rm -f participant-admin_participant_db
	docker builder prune -a -f

term:
	docker run -it participant-admin:dev-latest bash
