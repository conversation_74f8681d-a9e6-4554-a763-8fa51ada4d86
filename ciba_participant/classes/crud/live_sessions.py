from uuid import UUID
from datetime import datetime, timezone as tz
from typing import Optional

import pendulum
import tortoise
import tortoise.transactions
from tortoise.transactions import in_transaction
from zoneinfo import ZoneInfo

from ciba_participant.common.converters import from_utc_to_pst
from ciba_participant.dates.service import has_conflict
from ciba_participant.error_messages.classes import (
    LIVE_SESSION_NOT_FOUND,
    RECORDING_EXISTS_ALREADY,
    ERROR_TIME_LIMIT_FOR_DELETE,
    ERROR_SENDING_EMAIL,
)
from ciba_participant.schedule_manager.service import (
    ScheduleManager,
    ZoomRecurrenceType,
    CreateZoomMeetingOutput,
)
from ciba_participant.classes.models import (
    LiveSession,
    RawLiveSession,
    Booking,
    MeetingTypeEnum,
)
from ciba_participant.participant.crud import AuthorizedRepository
from ciba_participant.participant.models import Authorized, Participant
from ciba_participant.classes.crud.types import WebinarCreate
from ciba_participant.classes.errors import LiveSessionError
from ciba_participant.dates.service import get_interval_and_frequency

from ciba_participant.common.aws_handler import (
    SQSNotification,
    send_to_sqs,
    EmailNotificationEvent,
    NotificationType,
)
from ciba_participant import get_settings

settings = get_settings()


async def create(
    data: WebinarCreate,
    host: Authorized,
    webinar_id: UUID,
) -> None:
    schedule_manager = ScheduleManager()

    alternative_hosts = [
        alt_host.email
        for alt_host in await AuthorizedRepository.get_alternative_hosts()
        if alt_host.id != host.id
    ]

    interval, frequency = get_interval_and_frequency(data.recurrence)

    zoom_meetings: CreateZoomMeetingOutput = (
        await schedule_manager.create_zoom_meetings(
            duration=data.duration or 60,
            repeat_interval=interval,
            repeat_count=data.number_of_sessions,
            repeat_type=ZoomRecurrenceType[frequency.value],
            start_time=data.meeting_start_time,
            timezone="UTC",
            alternative_hosts=alternative_hosts,
            user_id=host.email,
            title=data.title,
            description=data.description,
        )
    )

    for occurrence in zoom_meetings.occurrences:
        await LiveSession.create(
            title=data.title,
            description=data.description,
            meeting_start_time=occurrence.start_time,
            webinar_id=webinar_id,
            zoom_id=zoom_meetings.meeting_id,
            zoom_occurrence_id=occurrence.id,
            timezone=data.timezone,
            has_conflict=has_conflict(occurrence.start_time),
        )


async def get(live_session_id: UUID) -> RawLiveSession:
    live_session = await LiveSession.get_or_none(id=live_session_id)

    if not live_session:
        raise LiveSessionError(LIVE_SESSION_NOT_FOUND)

    return RawLiveSession.model_validate(live_session)


async def update(
    id: UUID,
    *,
    title: Optional[str] = None,
    description: Optional[str] = None,
    start_time: Optional[datetime] = None,
    timezone: Optional[str] = None,
    use_custom_meeting_link: Optional[bool] = None,
    custom_meeting_link: Optional[str] = None,
):
    async with in_transaction():
        schedule_manager = ScheduleManager()

        live_session = await LiveSession.get(id=id)

        if not live_session:
            raise LiveSessionError(LIVE_SESSION_NOT_FOUND)

        async with tortoise.transactions.in_transaction():
            if title and title != live_session.title:
                live_session.title = title

            if description and description != live_session.description:
                live_session.description = description

            if start_time:
                if timezone and start_time.tzinfo is None:
                    start_time = start_time.replace(tzinfo=ZoneInfo(timezone))
                    live_session.timezone = timezone

                start_time_utc = start_time.astimezone(tz.utc)

                live_session.meeting_start_time = start_time_utc
                live_session.has_conflict = has_conflict(start_time_utc)

            if (
                use_custom_meeting_link is not None
                and use_custom_meeting_link != live_session.use_custom_meeting_link
            ):
                live_session.use_custom_meeting_link = use_custom_meeting_link

            if live_session.use_custom_meeting_link:
                if custom_meeting_link is not None:
                    live_session.custom_meeting_link = custom_meeting_link

            new_meeting = await schedule_manager.update_zoom_meeting_occurrence(
                meeting_id=live_session.zoom_id,
                occurrence_id=live_session.zoom_occurrence_id,
                start_time=live_session.meeting_start_time.astimezone(
                    ZoneInfo(live_session.timezone)
                ),
                timezone=live_session.timezone,
                topic=live_session.title,
                agenda=live_session.description,
            )

            live_session.zoom_id = new_meeting.meeting_id
            live_session.zoom_occurrence_id = "None"

            await live_session.save()


async def delete(live_session_id: UUID):
    async with in_transaction():
        schedule_manager = ScheduleManager()

        live_session = await LiveSession.get_or_none(id=live_session_id)

        if live_session is None:
            raise LiveSessionError(LIVE_SESSION_NOT_FOUND)

        max_time = pendulum.now().add(minutes=10)

        if live_session.meeting_start_time <= max_time:
            raise LiveSessionError(ERROR_TIME_LIMIT_FOR_DELETE)

        if live_session.meeting_type == MeetingTypeEnum.RECURRING_WITH_FIXED_TIME:
            await schedule_manager.delete_zoom_meeting_occurrence(
                live_session.zoom_id, live_session.zoom_occurrence_id
            )
        else:
            await schedule_manager.delete_zoom_meeting(live_session.zoom_id)

        bookings = await Booking.filter(live_session_id=live_session_id)

        for b in bookings:
            participant = await Participant.get(id=b.participant_id)
            class_date = from_utc_to_pst(
                started_call=live_session.meeting_start_time,
                date_format="MM/DD/YYYY hh:mm A zz",
            )

            notification = SQSNotification(
                type=NotificationType.SQS,
                email_event=EmailNotificationEvent.CANCELLED_SESSION,
                data={
                    "email": participant.email,
                    "first_name": participant.first_name,
                    "class_name": live_session.title,
                    "class_date": class_date,
                },
            )

            try:
                send_to_sqs(
                    queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
                    message_body=notification.model_dump_json(),
                )
            except Exception as e:
                raise LiveSessionError(ERROR_SENDING_EMAIL) from e

            await b.delete()

        await live_session.delete()


async def upload_recording(live_session_id: UUID, video_url: str = ""):
    """Find the live session and get the recording URL from Zoom API"""
    async with in_transaction():
        schedule_manager = ScheduleManager()

        live_session = await LiveSession.get_or_none(id=live_session_id)

        if not live_session:
            raise LiveSessionError(LIVE_SESSION_NOT_FOUND)

        if live_session.recording_url:
            raise LiveSessionError(RECORDING_EXISTS_ALREADY)

        # If we got video_url, save it as is
        if video_url:
            live_session.recording_url = video_url

        # If we do not have video_url, get recording from Zoom API
        else:
            recording_output = await schedule_manager.get_meeting_recordings(
                live_session.zoom_id
            )
            live_session.recording_url = recording_output.recording_url

        notification = SQSNotification(
            type=NotificationType.SQS,
            email_event=EmailNotificationEvent.CLASS_RECORDING_AVAILABLE,
            data={
                "live_session_id": live_session_id,
            },
        )
        send_to_sqs(
            queue_url=settings.PARTICIPANT_EMAIL_SQS_URL,
            message_body=notification.model_dump_json(),
        )

        await live_session.save()


async def update_recording(live_session_id: UUID, video_url: str):
    """Find the live session and get the recording URL from Zoom API"""
    async with in_transaction():
        live_session = await LiveSession.get_or_none(id=live_session_id)

        if not live_session:
            raise LiveSessionError(RECORDING_EXISTS_ALREADY)

        live_session.recording_url = video_url

        await live_session.save()


async def delete_recording(
    live_session_id: UUID,
):
    """Find the live session and get the recording URL from Zoom API"""
    async with in_transaction():
        live_session = await LiveSession.get_or_none(id=live_session_id)

        if not live_session:
            raise LiveSessionError(RECORDING_EXISTS_ALREADY)

        live_session.recording_url = ""

        await live_session.save()
