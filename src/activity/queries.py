from uuid import UUID

import strawberry
from typing import List, Optional
from ciba_participant.activity.crud import ParticipantActivityRepository

from src.activity.types import ParticipantActivityGraphType
from src.activity.inputs import ParticipantActivityIDInput
from src.log.logging import logger


async def get_participant_activities_ids(participant_id: UUID) -> List[UUID]:
    """Get participant activities ids resolver"""
    try:
        activities = await ParticipantActivityRepository.get_activities_ids_by_participant_id(
            participant_id=participant_id
        )
        return activities
    except Exception as e:
        logger.exception(e)
        return []


async def get_participant_activities() -> List[ParticipantActivityGraphType]:
    """Get participant activities resolver"""
    try:
        activities = (
            await ParticipantActivityRepository.get_participant_activities(
                1, 10
            )
        )
        return activities
    except Exception as e:
        logger.exception(e)
        return []


async def get_participant_activity(
    data: ParticipantActivityIDInput,
) -> Optional[ParticipantActivityGraphType]:
    """Get participant activity resolver"""
    try:
        activity = (
            await ParticipantActivityRepository.get_participant_activity(
                data.id
            )
        )
        return activity
    except Exception as e:
        logger.exception(e)
        return None


@strawberry.type
class ActivityQuery:
    """Query for activities"""

    get_participant_activities_ids: List[UUID] = strawberry.field(
        resolver=get_participant_activities_ids
    )
    get_participant_activities: List[ParticipantActivityGraphType] = (
        strawberry.field(resolver=get_participant_activities)
    )
    get_participant_activity: Optional[ParticipantActivityGraphType] = (
        strawberry.field(resolver=get_participant_activity)
    )
