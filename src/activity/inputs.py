from uuid import UUID
from typing import Optional

import strawberry

from src.activity.types import (
    PATypeGraphEnum,
    PACategoryGraphEnum,
    PADeviceGraphEnum,
    PAUnitGraphEnum,
)


@strawberry.input
class ParticipantActivityInput:
    participant_id: UUID
    value: str
    unit: PAUnitGraphEnum
    activity_device: PADeviceGraphEnum
    activity_category: PACategoryGraphEnum
    activity_type: PATypeGraphEnum


@strawberry.input
class ParticipantActivityUpdateInput:
    id: UUID
    participant_id: Optional[UUID] = None
    value: Optional[float] = None
    unit: Optional[PAUnitGraphEnum] = None
    activity_device: Optional[PADeviceGraphEnum] = None
    activity_category: Optional[PACategoryGraphEnum] = None
    activity_type: Optional[PATypeGraphEnum] = None


@strawberry.input
class ParticipantActivityIDInput:
    id: UUID
