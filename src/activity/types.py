from typing import List
from uuid import UUID
from datetime import datetime

import strawberry
from ciba_participant.activity.models import (
    ActivityUnit,
    ParticipantActivityCategory,
    ParticipantActivityDevice,
    ParticipantActivityEnum,
)

PATypeGraphEnum = strawberry.enum(ParticipantActivityEnum)
PACategoryGraphEnum = strawberry.enum(ParticipantActivityCategory)
PADeviceGraphEnum = strawberry.enum(ParticipantActivityDevice)
PAUnitGraphEnum = strawberry.enum(ActivityUnit)


@strawberry.type
class ParticipantActivityGraphType:
    id: UUID
    participant_id: UUID
    activity_type: PATypeGraphEnum
    activity_category: PACategoryGraphEnum
    activity_device: PADeviceGraphEnum
    unit: PAUnitGraphEnum
    created_at: datetime
    value: str


@strawberry.type
class ParticipantActivityListType:
    activities: List[ParticipantActivityGraphType]
