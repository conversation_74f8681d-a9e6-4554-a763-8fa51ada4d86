import http
import logging
import math
import time

from fastapi import Request, Response
from starlette.middleware.base import RequestResponseEndpoint

from src.log.logging import PASS_ROUTES, logger
from src.log.schemas import RequestJsonLogSchema

EMPTY_VALUE = ""


class LoggingMiddleware:
    """Middleware that converts logs into JSON format."""

    async def __call__(
        self,
        request: Request,
        call_next: RequestResponseEndpoint,
        *args: str,
        **kwargs: str,
    ) -> Response:
        """Log Middleware for incoming requests."""
        start_time = time.time()
        exception_object = None
        server: tuple = request.get("server", ("localhost", 8000))
        request_headers: dict = dict(request.headers.items())

        # Response Side
        try:
            response = await call_next(request)
        except Exception as ex:  # pylint: disable=broad-except
            logging.error("Exception: %s", ex)
            response_body = bytes(
                http.HTTPStatus.INTERNAL_SERVER_ERROR.phrase.encode()
            )
            response = Response(
                content=response_body,
                status_code=http.HTTPStatus.INTERNAL_SERVER_ERROR.real,
            )
            exception_object = ex

        # pass /openapi.json /docs /health /ready
        if request.url.path in PASS_ROUTES and response.status_code == 200:
            return response

        duration: int = math.ceil((time.time() - start_time) * 1000)

        # Initializing of json fields
        request_json_fields = RequestJsonLogSchema(
            # Request side
            request_uri=str(request.url),
            request_referer=request_headers.get("referer", EMPTY_VALUE),
            request_method=request.method,
            request_path=request.url.path,
            request_host=f"{server[0]}:{server[1]}",
            request_size=int(request_headers.get("content-length", 0)),
            request_content_type=request_headers.get(
                "content-type", EMPTY_VALUE
            ),
            # Response side
            response_status_code=response.status_code,
            duration=duration,
        ).dict()

        message = (
            f"{'Error' if exception_object else 'Answer'} "
            f"code: {response.status_code} "
            f'request url: {request.method} "{str(request.url)}" '
            f"duration: {duration} ms "
        )
        logger.info(
            message,
            extra={
                "request_json_fields": request_json_fields,
                "to_mask": True,
            },
            exc_info=exception_object,
        )
        return response
