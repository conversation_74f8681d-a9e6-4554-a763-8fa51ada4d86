from datetime import datetime
from typing import Any, Optional
from uuid import UUID

import strawberry
from pydantic import BaseModel

from src.participant.inputs import AuthorizedRoleEnum, ParticipantStatusEnum


class JWTAuth(BaseModel):
    # issuer
    iss: str
    # subject
    # for Cognito: The unique identifier for your user.
    sub: str
    # for Cognito: The user pool app client that authenticated your user.
    aud: str
    # Oauth client_id
    # for Cognito: same as aud, the user pool app client that authenticated your user.
    client_id: str | None = None
    # The authentication time, in Unix time format, time when the authentication occurred
    auth_time: int | None = None
    # The expiration time of the token, in Unix time format.
    exp: int | None = None
    # The issued-at time, in Unix time format, that Amazon Cognito issued your user's token.
    iat: int | None = None
    # The unique identifier for the token.
    jti: str | None = None


class _CognitoAuth(BaseModel):
    email_verified: bool
    email: str
    # A token-revocation identifier associated with your user's refresh token.
    origin_jti: str | None = None
    # Traces the event that originated the token.
    event_id: str | None = None
    # The intended purpose of the token. In an ID token, its value is "id".
    token_use: str | None = None


class CibaAuth(BaseModel):
    # custom:isAdmin
    is_admin: str | None = None
    # custom:isParticipantAdmin
    is_participant_admin: str | None = None
    # custom:passwordExpiredAt
    password_expired_at: datetime | None = None
    # custom:passwordUpdatedAt
    password_updated_at: datetime | None = None
    nickname: str | None = None
    # cognito:username
    username: str | None = None


class CognitoAuth(BaseModel):
    jwt: JWTAuth
    cognito: _CognitoAuth
    ciba: CibaAuth

    @classmethod
    def model_validate(
        cls,
        obj: dict,
        *,
        strict: bool | None = None,
        from_attributes: bool | None = None,
        context: Any | None = None,
    ) -> "CognitoAuth":
        return cls(
            jwt=JWTAuth.model_validate(obj, from_attributes=True),
            cognito=_CognitoAuth.model_validate(obj, from_attributes=True),
            ciba=CibaAuth(
                is_admin=obj.get("custom:isAdmin"),
                is_participant_admin=obj.get("custom:isParticipantAdmin"),
                password_expired_at=obj.get("custom:passwordExpiredAt"),
                nickname=obj.get("nickname"),
                username=obj.get("cognito:username"),
                password_updated_at=obj.get("custom:passwordUpdatedAt"),
            ),
        )


@strawberry.type
class UserType:
    id: UUID
    email: str
    email_verified: bool


@strawberry.type
class AuthorizedType:
    id: UUID
    email: str
    first_name: str
    last_name: str
    chat_identity: str
    role: AuthorizedRoleEnum  # type: ignore
    status: ParticipantStatusEnum  # type: ignore
    cognito_sub: Optional[UUID] = None
    is_test: Optional[bool] = None
    api_id: Optional[UUID] = None
    support_in_chat: Optional[bool] = None
    classes_admin: Optional[bool] = None
    content_admin: bool = False
    cohort_admin: bool = False
    created_at: datetime
    updated_at: datetime


@strawberry.type
class AuthorizedListType:
    authorized: list[AuthorizedType]
