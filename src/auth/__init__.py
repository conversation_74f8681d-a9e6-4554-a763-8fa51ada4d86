from dataclasses import dataclass
from datetime import timedelta
from functools import cache, cached_property
from typing import Protocol
from uuid import UUID

import jwt
import jwt.algorithms
from asyncstdlib.functools import cached_property as async_cached_property
from ciba_participant.cohort.models import Authorized
from src.log.logging import logger
from strawberry.fastapi import BaseContext

from src.settings import get_settings

from .errors import (
    MissingAuthorizationHeaderError,
    MissingTokenError,
)
from .jwks import get_jwks_cache
from .types import AuthorizedType, CognitoAuth, UserType


@cache
def get_cognito_issuer(region: str, pool_id: str):
    return f"https://cognito-idp.{region}.amazonaws.com/{pool_id}"


@cache
def get_cognito_jwks_url(region: str, pool_id: str):
    return f"{get_cognito_issuer(region, pool_id)}/.well-known/jwks.json"


class WithToken(Protocol):
    @cached_property
    def token(self) -> str: ...


class WithAuth[T](Protocol):
    @cached_property
    def auth(self) -> T: ...


class WithUser(Protocol):
    @cached_property
    def user(self) -> UserType: ...


class WithAuthorized(Protocol):
    @async_cached_property
    async def authorized(self) -> AuthorizedType | None: ...


class WithCognitoAuth(WithAuth[CognitoAuth]):
    pass


class WithBearerToken(BaseContext):
    @cached_property
    def token(self) -> str:
        if not self.request:
            raise ValueError("Request is not set")

        header_value = self.request.headers.get("Authorization")

        if not header_value:
            raise MissingAuthorizationHeaderError()

        if not header_value.startswith("Bearer "):
            raise MissingTokenError()

        try:
            token = header_value.split("Bearer ")[1]
        except IndexError:
            raise MissingTokenError()

        if not token:
            raise MissingTokenError()

        return token


@dataclass(kw_only=True)
class CognitoInfo:
    issuer: str
    jwks_url: str


def get_cognito_info(region: str, pool_id: str) -> CognitoInfo:
    return CognitoInfo(
        issuer=get_cognito_issuer(region, pool_id),
        jwks_url=get_cognito_jwks_url(region, pool_id),
    )


DAY_SECONDS = timedelta(days=1).total_seconds()


class InvalidTokenError(Exception):
    def __init__(self, reason: str = ""):
        super().__init__(f"Invalid token: {reason}")
        self.reason = reason


class WithVerifiedCognitoAuth(WithToken, WithCognitoAuth):
    """
    https://docs.aws.amazon.com/cognito/latest/developerguide/amazon-cognito-user-pools-using-tokens-verifying-a-jwt.html
    https://github.com/awslabs/aws-support-tools/blob/master/Cognito/decode-verify-jwt/decode-verify-jwt.py
    """

    @cached_property
    def auth(self) -> CognitoAuth:
        settings = get_settings()

        cognito = get_cognito_info(
            settings.COGNITO_AWS_REGION,
            settings.PROVIDERS_COGNITO_USER_POOL_ID,
        )

        try:
            header = jwt.get_unverified_header(self.token)
        except jwt.DecodeError as de:
            raise InvalidTokenError(de.__cause__)
        except jwt.InvalidTokenError as te:
            raise InvalidTokenError(te.__cause__)

        kid = header.get("kid")

        if not kid:
            raise InvalidTokenError("kid not found")

        # Cache the jwks for 1 day
        jwks_cache = get_jwks_cache(cognito.jwks_url, ttl=DAY_SECONDS)

        key = jwks_cache.get_key(kid)

        if not key:
            raise InvalidTokenError("public key not found")

        public_key = jwt.PyJWK.from_dict(
            algorithm=key.alg, obj=key.model_dump()
        )

        # by default jwt.decode verifies
        # - message is not malformed (recommended by cognito)
        # - signature is valid (recommended by cognito)
        # - token is not expired (recommended by cognito)
        # - aud matches audience parameter (which for Cognito is the user pool id) (recommended by cognito)
        token_object = jwt.decode(
            self.token,
            key=public_key,
            algorithms=[key.alg],
            audience=settings.PROVIDERS_COGNITO_APP_CLIENT_ID,
            issuer=cognito.issuer,
        )

        # Token use must be id (recommended by cognito as we only require an id token)
        if token_object["token_use"] != "id":
            raise InvalidTokenError("invalid token use")

        return CognitoAuth.model_validate(token_object)


class WithUnverifiedCognitoAuth(WithToken, WithCognitoAuth):
    @cached_property
    def auth(self) -> CognitoAuth:
        logger.warning("Decoding token without verifying signature")

        try:
            token_object = jwt.decode(
                self.token,
                algorithms=["RS256"],
                options={"verify_signature": False},
            )
        except Exception:
            raise Exception("Invalid token")

        return CognitoAuth.model_validate(token_object)


class WithCognitoUser(WithCognitoAuth):
    @cached_property
    def user(self) -> UserType:
        sub = UUID(self.auth.jwt.sub)

        return UserType(
            id=sub,
            email=self.auth.cognito.email,
            email_verified=self.auth.cognito.email_verified,
        )


class WithAuthorizedUser(WithCognitoUser):
    @async_cached_property
    async def authorized(self) -> AuthorizedType | None:
        user = self.user
        authorized = await Authorized.get_or_none(cognito_sub=user.id)

        if authorized is None:
            return None

        return AuthorizedType(
            id=authorized.id,
            email=authorized.email,
            first_name=authorized.first_name,
            last_name=authorized.last_name,
            chat_identity=authorized.chat_identity,
            api_id=authorized.api_id,
            role=authorized.role,
            status=authorized.status,
            created_at=authorized.created_at,
            updated_at=authorized.updated_at,
            classes_admin=authorized.classes_admin,
            content_admin=authorized.content_admin,
            cohort_admin=authorized.full_admin(),
        )
