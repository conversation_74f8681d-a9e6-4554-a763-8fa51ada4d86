import strawberry

from src.auth.types import AuthorizedType, UserType
from src.context import Context


@strawberry.type
class MeType:
    user: UserType
    authorized: AuthorizedType | None = None


@strawberry.type
class AuthQuery:
    @strawberry.field
    async def me(self, info: strawberry.Info[Context]) -> MeType:
        user = info.context.user
        authorized = await info.context.authorized

        return MeType(user=user, authorized=authorized)
