import time
from functools import cache

import httpx
from pydantic import BaseModel


class JWK(BaseModel):
    alg: str
    e: str
    kid: str
    kty: str
    n: str
    use: str


class JWKS(BaseModel):
    keys: list[JWK]


class JWKSCache:
    _client = httpx.Client()

    def __init__(
        self, jwks_url: str, ttl: int, immediate_refresh: bool = True
    ):
        self.jwks_url = jwks_url
        self.ttl = ttl

        if immediate_refresh:
            self.refresh()

    def _fetch_jwks(self) -> JWKS:
        response = JWKSCache._client.get(self.jwks_url)
        return JWKS.model_validate(response.json())

    def _fetch_keys(self):
        self._jwks = self._fetch_jwks()
        self._keys = {key.kid: key for key in self._jwks.keys}

    def refresh(self):
        self._fetch_keys()
        self._start = time.time()
        self._just_refreshed = True

    def get_key(self, kid: str):
        self._just_refreshed = False

        if time.time() - self._start > self.ttl:
            self.refresh()

        if key := self._keys.get(kid):
            return key

        if self._just_refreshed:
            return None

        self.refresh()

        if key := self._keys.get(kid):
            return key


@cache
def get_jwks_cache(jwks_url: str, ttl: int, immediate_refresh: bool = True):
    return JWKSCache(jwks_url, ttl, immediate_refresh)
