from collections.abc import Awaitable
from functools import wraps
from typing import Callable, ParamSpec, TypeVar

import strawberry

from src.common.utils import ensure_info_param

from . import WithAuthorizedUser
from .errors import (
    UnauthorizedError,
)

Param = ParamSpec("Param")
RetType = TypeVar("RetType")


def is_authorized(
    error_msg: str = "Unauthorized",
):
    def decorator(fn: Callable[Param, Awaitable[RetType]]):
        # This add the info parameter to the function signature if it doesn't exist
        info_param = ensure_info_param(fn)

        @wraps(fn)
        async def wrapper(*args: Param.args, **kwargs: Param.kwargs):
            try:
                info: strawberry.Info[WithAuthorizedUser] = kwargs.get(
                    info_param.name
                )  #  type: ignore
                authorized = await info.context.authorized
            except Exception:
                raise UnauthorizedError(error_msg)

            if not authorized:
                raise UnauthorizedError(error_msg)

            # We create a copy of the kwargs to avoid modifying the original
            kwargs_copy = kwargs.copy()

            # If the info parameter was added by the decorator, remove it
            if not info_param.already_existed:
                kwargs_copy.pop(info_param.name)

            return await fn(*args, **kwargs_copy)  # type: ignore

        return wrapper

    return decorator
