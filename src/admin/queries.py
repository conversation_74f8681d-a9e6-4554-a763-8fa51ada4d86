import logging
from typing import Optional

import strawberry
from ciba_participant.cohort.models import <PERSON><PERSON><PERSON>
from ciba_participant.participant.crud import AuthorizedRepository
from ciba_participant.participant.models import Authorized, AutorizedRole
from ciba_participant.participant.data_preparer import extend_provider_data
from src.log.logging import logger

from src.auth.types import AuthorizedListType, AuthorizedType


@strawberry.input
class GetProvidersFilterInput:
    role: AutorizedRole


async def get_authorized_users() -> AuthorizedListType:
    """Return list of authorized users, by role"""

    users = await AuthorizedRepository.list_active_authorized()

    try:
        provider_type_users = AuthorizedListType(authorized=[])
        for user in users:
            user_data = AuthorizedType(
                id=user.id,
                email=user.email,
                first_name=user.first_name,
                last_name=user.last_name,
                chat_identity=user.chat_identity,
                role=user.role,
                status=user.status,
                created_at=user.created_at,
                updated_at=user.updated_at,
            )
            provider_type_users.authorized.append(user_data)
        return provider_type_users

    except Exception as e:
        logger.exception(e)
        raise e


async def get_providers(
    filters: Optional[GetProvidersFilterInput] = None,
) -> AuthorizedListType:
    """Return list of authorized users, by role"""

    provider_data = await extend_provider_data(filters)

    try:
        provider_type_users = AuthorizedListType(authorized=[])
        for user in provider_data:
            user_data = AuthorizedType(
                id=user["id"],
                email=user["email"],
                first_name=user["first_name"],
                last_name=user["last_name"],
                chat_identity=user["chat_identity"],
                role=user["role"],
                status=user["status"],
                created_at=user["created_at"],
                updated_at=user["updated_at"],
            )
            provider_type_users.authorized.append(user_data)
        return provider_type_users
    except Exception as e:
        logger.exception(e)
        raise e


async def get_cohorts_creators() -> AuthorizedListType:
    """Return list of admins and providers, that has created a cohort"""
    try:
        creators_ids = (
            await Cohort.all()
            .distinct()
            .values_list("created_by_id", flat=True)
        )

        creators = await Authorized.filter(id__in=creators_ids)

        provider_type_creators = AuthorizedListType(authorized=[])
        for creator in creators:
            admin_data = AuthorizedType(
                id=creator.id,
                email=creator.email,
                first_name=creator.first_name,
                last_name=creator.last_name,
                chat_identity=creator.chat_identity,
                role=creator.role,
                status=creator.status.value,
                created_at=creator.created_at,
                updated_at=creator.updated_at,
            )
            provider_type_creators.authorized.append(admin_data)
        logging.info(f"Returning cohort creators: {provider_type_creators}")
        return provider_type_creators
    except Exception as e:
        logger.exception(e)
        raise e


@strawberry.type
class AdminQuery:
    get_providers: AuthorizedListType = strawberry.field(
        resolver=get_providers
    )
    get_cohorts_creators: AuthorizedListType = strawberry.field(
        resolver=get_cohorts_creators
    )
    get_authorized_users: AuthorizedListType = strawberry.field(
        resolver=get_authorized_users
    )
