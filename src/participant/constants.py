ADD_TO_GROUP_CHAT_MUTATION = """
    mutation MyMutation(
        $uniqueName: String!
        $participant: ConversationParticipantInput!
        ) {
      addParticipantToConversation(
        uniqueName: $uniqueName
        participant: $participant
      ){
        ... on ConversationType {
          createdAt
          updatedAt
          id
          type
          sid
          uniqueName
          friendlyName
        }
        ... on ConversationDneError {
          message
        }
      }
    }
"""
