from typing import List, Optional

import pandas as pd
from ciba_participant.activity.models import ParticipantActivity

from src.participant.types import DayWeightItem, WeightHistoryItem

WEIGHT_LOWER_LIMIT = 10


def map_to_weight_item(activity: ParticipantActivity) -> WeightHistoryItem:
    """
    Method to map an Activity model into a Weight item.
    """
    return WeightHistoryItem(
        date=activity.created_at,
        weight=float(activity.value) if activity.value else None,
        source=activity.activity_device.value,
    )

def get_weight_history_activities(
    weight_activities: List[ParticipantActivity],
    index_column: Optional[str] = "date",  # e.g. "date" or None = no index
    resample_freq: Optional[
        str
    ] = None,  # e.g. "d" for daily, None = no resampling
) -> List[WeightHistoryItem]:
    """
    Get a list of weight activities with optional resampling.

    Args:
        weight_activities: List of ParticipantActivity objects.
        index_column: Column to use as index (default = "date").
        resample_freq: Pandas resample frequency (e.g. "d" for daily).
                       If None, returns the raw list without resampling.
    """
    if not weight_activities:
        return []

    mapped_activities = map(map_to_weight_item, weight_activities)

    dataframe = pd.DataFrame(
        mapped_activities, columns=["date", "weight", "source"]
    ).sort_values(by="date", ascending=True)
    dataframe = dataframe[dataframe["weight"] > WEIGHT_LOWER_LIMIT]

    if dataframe.empty:
        return []

    if index_column:
        dataframe = dataframe.set_index(index_column)

    if resample_freq:
        dataframe = (
            dataframe.resample(resample_freq)
            .last()
            .sort_index(ascending=True)
            .dropna()
            .reset_index()
        )
    else:
        dataframe = dataframe.reset_index(drop=True)

    weight_history = [
        WeightHistoryItem(
            date=row["date"],
            weight=None if pd.isna(row["weight"]) else row["weight"],
            source=row["source"],
        )
        for _, row in dataframe.iterrows()
    ]

    return weight_history
