# ciba_participant/admin/graphql/dataloaders.py
from strawberry.dataloader import Data<PERSON>oader
from typing import List
from uuid import UUID
from ciba_participant.participant.crud import (
    ParticipantRepository,
    AuthorizedRepository,
)
from src.participant.types import ParticipantType
from src.auth.types import AuthorizedType


async def batch_load_participants(
    keys: List[UUID],
) -> List[List[ParticipantType]]:
    participants = await ParticipantRepository.get_participants_by_cohort_ids(
        keys
    )
    participants_map = {key: [] for key in keys}
    for participant in participants:
        participants_map[participant.cohort_id].append(participant)
    return [participants_map[key] for key in keys]


participant_dataloader = DataLoader(load_fn=batch_load_participants)


async def batch_load_authorized_by_schedule_id(
    schedule_ids: List[int],
) -> List[AuthorizedType | ValueError]:
    async def lookup(key: int) -> AuthorizedType | ValueError:
        if authorized := await AuthorizedRepository.get_by_schedule_id(key):
            return authorized

        return ValueError(f"Authorized not found for schedule_id: {key}")

    return [await lookup(key) for key in schedule_ids]


authorized_loader_by_schedule_id = DataLoader(
    load_fn=batch_load_authorized_by_schedule_id
)
