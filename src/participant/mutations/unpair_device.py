from uuid import UUID

from ciba_participant.log.logging import logger
from ciba_participant.rpm_api.api import disconnect_device
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.participant.models import Participant
from ciba_participant.rpm_api.models import DeviceTypeEnum
from src.common.types import DetailedResponse


async def unpair_device(
    participant_id: UUID,
    device_id: UUID,
) -> DetailedResponse:
    """Unpair device from participant."""
    try:
        participant = await Participant.filter(id=participant_id).get_or_none()

        if participant is None:
            raise ValueError("Participant not found")

        await disconnect_device(
            participant_id=participant_id,
            device_id=device_id,
            device_type=DeviceTypeEnum.TRANSTEK,
        )

    except RPMCallError as e:
        logger.exception(f"Error unpairing device: {e}")
        return DetailedResponse(success=False, message=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error unpairing device: {e}")
        return DetailedResponse(success=False, message="Unexpected error")

    return DetailedResponse(
        success=True, message="Device unpaired successfully"
    )
