from typing import Optional
from uuid import UUID

from ciba_participant.log.logging import logger
from ciba_participant.rpm_api.api import pair_transtek_device
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.participant.models import Participant
from src.common.types import DetailedResponse


async def pair_device(
    participant_id: UUID,
    imei: Optional[str] = None,
    serial_number: Optional[str] = None,
) -> DetailedResponse:
    """Pair device with participant."""
    try:
        participant = await Participant.filter(id=participant_id).get_or_none()

        if participant is None:
            raise ValueError("Participant not found")

        await pair_transtek_device(
            participant_id=participant_id,
            participant_email=participant.email,
            imei=imei,
            serial_number=serial_number,
        )

    except RPMCallError as e:
        logger.exception(f"Error pairing device: {e}")
        return DetailedResponse(success=False, message=str(e))
    except Exception as e:
        logger.exception(f"Unexpected error pairing device: {e}")
        return DetailedResponse(success=False, message="Unexpected error")

    return DetailedResponse(success=True, message="Devi<PERSON> paired successfully")
