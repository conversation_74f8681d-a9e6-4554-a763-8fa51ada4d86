import strawberry
from typing import Optional
from uuid import UUID
from datetime import datetime

from strawberry.scalars import JSO<PERSON>
from ciba_participant.participant.crud import FieldEnum, OrderEnum
from ciba_participant.participant.models import (
    ParticipantStatus,
    AutorizedRole,
)

ParticipantStatusEnum = strawberry.enum(ParticipantStatus)
AuthorizedRoleEnum = strawberry.enum(AutorizedRole)


@strawberry.input
class AuthorizedCreateInput:
    email: str
    first_name: str
    last_name: str
    role: AuthorizedRoleEnum = AuthorizedRoleEnum.HEALTH_COACH


@strawberry.input
class AuthorizedUpdateInput:
    id: UUID
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[AuthorizedRoleEnum] = None
    status: Optional[ParticipantStatusEnum] = None


@strawberry.input
class ParticipantCreateInput:
    email: str
    first_name: str
    last_name: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatusEnum = ParticipantStatusEnum.PENDING
    cognito_sub: Optional[UUID] = None
    medical_record: Optional[str] = None
    is_test: bool = False
    last_reset: Optional[datetime] = None


@strawberry.input
class ParticipantUpdateInput:
    id: UUID
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    group_id: Optional[str] = None
    member_id: Optional[str] = None
    status: Optional[ParticipantStatusEnum] = None
    cognito_sub: Optional[UUID] = None
    medical_record: Optional[str] = None
    is_test: Optional[bool] = None
    last_reset: Optional[datetime] = None


@strawberry.input
class ParticipantIDInput:
    id: UUID


@strawberry.input
class SoleraParticipantCreateInput:
    participant_id: UUID
    solera_id: Optional[UUID]
    solera_key: str
    solera_program_id: str
    solera_enrollment_id: str


@strawberry.input
class SoleraParticipantUpdateInput:
    id: UUID
    participant_id: Optional[UUID]
    solera_id: Optional[UUID]
    solera_key: Optional[str]
    solera_program_id: Optional[str]
    solera_enrollment_id: Optional[str]


@strawberry.input
class SoleraParticipantIDInput:
    participant_id: UUID


@strawberry.input
class HeadsUpParticipantCreateInput:
    participant_id: UUID
    heads_up_token: Optional[str]
    heads_up_id: Optional[str]


@strawberry.input
class HeadsUpParticipantUpdateInput:
    id: UUID
    participant_id: Optional[UUID]
    heads_up_token: Optional[str]
    heads_up_id: Optional[str]


@strawberry.input
class HeadsUpParticipantIDInput:
    id: UUID


@strawberry.input
class ParticipantMetaCreateInput:
    participant_id: UUID
    metadata: JSON


@strawberry.input
class ParticipantMetaUpdateInput:
    id: UUID
    participant_id: Optional[UUID]
    metadata: JSON


@strawberry.input
class ParticipantMetaIDInput:
    id: UUID


@strawberry.input
class AssignParticipantChatInput:
    participant_id: UUID
    cohort_id: UUID
    type: str


@strawberry.input
class DateRangeInput:
    start: datetime
    end: datetime


@strawberry.input
class GetParticipantsFilterInput:
    search: Optional[str] = None
    created_by_id: Optional[UUID] = None
    cohort_id: Optional[UUID] = None
    program_id: Optional[UUID] = None
    date_enrolled_range: Optional[DateRangeInput] = None
    participant_status: Optional[ParticipantStatusEnum] = None


@strawberry.input
class GetParticipantsSortInput:
    field: strawberry.enum(FieldEnum)
    order: strawberry.enum(OrderEnum)
