from datetime import date, datetime
from enum import StrEnum, auto
from typing import List, Optional
from uuid import UUID

import strawberry

from common.types import ShortResponse
from src.activity.types import ParticipantActivityGraphType
from src.participant.inputs import ParticipantStatusEnum
from src.program.types import ProgramModuleType
from ciba_participant.rpm_api.models import DeviceStatus


@strawberry.enum
class StatusInCohort(StrEnum):
    INACTIVE = auto()
    AWAITING = auto()
    ACTIVE = auto()
    FINISHED = auto()


@strawberry.type
class AddressType:
    city: str
    state: str
    street_1: str
    street_2: str | None
    zip_code: str


@strawberry.type
class HeightType:
    feet: float
    inches: float = 0


@strawberry.type
class ParticipantType:
    id: UUID
    email: str
    first_name: str
    last_name: str
    chat_identity: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatusEnum
    status_in_cohort: StatusInCohort | None = None
    cognito_sub: Optional[UUID]
    medical_record: Optional[str]
    solera_careplan_id: Optional[str] = None
    is_test: bool
    last_reset: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    activities: List[ParticipantActivityGraphType] | None

    # fields from meta table
    starting_date: datetime | None = None
    age: int | None = None
    gender: str | None = None
    current_module: ProgramModuleType | None = None
    address: AddressType | None = None
    birthdate: date | None = None
    initial_weight: float | None = None
    height: HeightType | None = None
    phone: str | None = None
    current_week: str | None = None
    disenrolled_reason: str | None = None
    disenrollment_date: datetime | None = None
    user_reported_weight: float | None = None
    user_target_weight: float | None = None

    # field from rpm
    devices_status: list[DeviceStatus] | None = None


@strawberry.type
class ParticipantsListType:
    participants: List[ParticipantType]


@strawberry.type
class SoleraParticipantType:
    id: UUID
    participant_id: UUID
    solera_id: Optional[UUID]
    solera_key: str
    solera_program_id: str
    solera_enrollment_id: str
    created_at: datetime
    updated_at: datetime


@strawberry.type
class SoleraParticipantsListType:
    participants: List[SoleraParticipantType]


@strawberry.type
class HeadsUpParticipantType:
    id: UUID
    participant_id: UUID
    heads_up_token: str | None
    heads_up_id: str | None
    created_at: datetime
    updated_at: datetime


@strawberry.type
class HeadsUpParticipantsListType:
    participants: List[HeadsUpParticipantType]


@strawberry.type
class ParticipantMetaType:
    id: UUID
    participant_id: UUID
    metadata: str
    created_at: datetime
    updated_at: datetime


@strawberry.type
class ParticipantMetasListType:
    participants: List[ParticipantMetaType]


@strawberry.type
class HasActivityRecordError:
    """Error raised when trying to delete a section with activity associated with it."""

    message: str = (
        "Unable to delete sections with associated activity records."
    )


@strawberry.type
class ChatParticipantType:
    id: UUID
    external_type: str
    external_id: str
    chat_identity: str


@strawberry.type
class ConversationType:
    """Conversation model based graphql type."""

    id: UUID
    type: str
    sid: str
    unique_name: str
    friendly_name: str
    created_at: str
    updated_at: str


@strawberry.type
class ConversationDneError(Exception):
    """Error for cases when conversation dne."""

    message: str = "Conversation does not exist."


@strawberry.type
class TokenType:
    """Company model based graphql type."""

    token: str
    chat_identity: str


@strawberry.type
class ParticipantDneError(Exception):
    """Error for cases when participant dne."""

    message: str = "Participant does not exist."


@strawberry.type
class ParticipantNotInConversationError(Exception):
    """Error for cases when participant is not in conversation."""

    message: str = "Participant is not in conversation."


@strawberry.type
class WeightMeasure:
    weight: Optional[float]
    source: Optional[str] = None


@strawberry.type
class WeightHistoryItem(WeightMeasure):
    date: datetime


@strawberry.type
class DayWeightItem(WeightMeasure):
    date: date


@strawberry.type
class DeviceInfo:
    id: str
    device_type: str
    last_synced_at: datetime


@strawberry.type
class Measure:
    value: float
    unit: str
    created_at: datetime


@strawberry.type
class LatestMeasures(ShortResponse):
    last_ciba_sync: Optional[datetime] = None
    last_device_sync: Optional[datetime] = None
    devices: List[DeviceInfo] = strawberry.field(default_factory=list)
    measures: List[Measure] = strawberry.field(default_factory=list)
