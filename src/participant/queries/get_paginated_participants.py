from datetime import datetime
from enum import StrEnum
from typing import Optional
from uuid import UUID

import strawberry
from strawberry import Info
from ciba_participant.rpm_api.models import DeviceStatus
from src.log.logging import logger
from ciba_participant.participant.crud import (
    ParticipantRepository,
    FilterInput,
    SortInput,
)
from ciba_participant.rpm_api.api import get_devices_status_by_participant_ids
from src.cohort.types import CohortInfoType

from src.common.input import PaginationInput
from src.participant.inputs import (
    GetParticipantsFilterInput,
    GetParticipantsSortInput,
    ParticipantStatusEnum,
)


class TranslateField(StrEnum):
    participantMeta = "participant_meta"
    cohort = "cohort"
    activities = "activities"


@strawberry.type
class ParticipantInfoType:
    id: UUID
    email: str
    first_name: str
    last_name: str
    chat_identity: str
    phone: Optional[str]
    group_id: UUID
    member_id: UUID
    status: ParticipantStatusEnum
    cognito_sub: Optional[UUID]
    medical_record: Optional[str]
    is_test: bool
    last_reset: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    last_activity_date: Optional[datetime]
    date_enrolled: Optional[datetime]
    cohort: Optional[CohortInfoType]
    disenrolled_reason: Optional[str]
    disenrollment_date: Optional[datetime]
    device_status: list[DeviceStatus]


@strawberry.type
class ParticipantsInfoListType:
    participants: list[ParticipantInfoType]
    total_pages: int


async def get_paginated_participants(
    info: Info,
    pagination: Optional[PaginationInput] = PaginationInput(),
    filters: Optional[GetParticipantsFilterInput] = None,
    sort: Optional[GetParticipantsSortInput] = None,
) -> ParticipantsInfoListType:
    """Get paginated participants query. Check the participant repository for more details."""
    # Add include if needed for optimization
    if pagination is None:
        raise NotImplementedError("Pagination is required")

    filters_input: Optional[FilterInput] = None
    if filters:
        filters_input = FilterInput.model_validate(
            filters, from_attributes=True
        )

    sort_input: Optional[SortInput] = None
    if sort:
        sort_input = SortInput.model_validate(sort, from_attributes=True)

    participants_output = (
        await ParticipantRepository.get_paginated_participants(
            page=pagination.page,
            per_page=pagination.per_page,
            filters=filters_input,
            sort=sort_input,
        )
    )
    if not participants_output.participants:
        return ParticipantsInfoListType(
            participants=[], total_pages=participants_output.total_pages
        )

    logger.info(
        f"Fetched {len(participants_output.participants)} participants",
    )

    participants_ids: list[str] = list(
        map(lambda p: str(p.id), participants_output.participants)
    )
    statuses: dict = await get_devices_status_by_participant_ids(
        participants_ids
    )

    participants = [
        ParticipantInfoType(
            id=participant.id,
            email=participant.email,
            first_name=participant.first_name,
            last_name=participant.last_name,
            chat_identity=participant.chat_identity,
            phone=participant.phone,
            group_id=participant.group_id,
            member_id=participant.member_id,
            status=ParticipantStatusEnum(participant.status),
            cognito_sub=participant.cognito_sub,
            medical_record=participant.medical_record,
            is_test=participant.is_test,
            last_reset=participant.last_reset,
            created_at=participant.created_at,
            updated_at=participant.updated_at,
            last_activity_date=participant.last_activity_date,
            date_enrolled=participant.date_enrolled,
            cohort=participant.cohort,
            disenrolled_reason=participant.dissenrolled_reason,
            disenrollment_date=participant.disenrollment_date,
            device_status=statuses.get(str(participant.id), []),
        )
        for participant in participants_output.participants
    ]

    return ParticipantsInfoListType(
        participants=participants,
        total_pages=participants_output.total_pages,
    )
