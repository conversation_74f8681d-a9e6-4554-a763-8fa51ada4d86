from datetime import datetime
from typing import Optional
from uuid import UUID

import pendulum
import strawberry
from tortoise.exceptions import BaseORMException

from ciba_participant.classes.models import (
    RawBooking,
    BookingStatusEnum,
    RawLiveSession,
)
from ciba_participant.common.validations.dates import validate_date_range
from ciba_participant.log.logging import logger
from ciba_participant.participant.crud import ParticipantRepository
from ciba_participant.participant.models import (
    Participant,
    RawParticipant,
    RawAuthorized,
)
from ciba_participant.participant.pydantic_models import (
    ParticipantClassProgress,
)

from src.common.errors import ParticipantNotFoundError
from src.content_library.messages import DB_READ_ERROR
from src.auth import AuthorizedType
from src.participant.types import ParticipantType
from src.webinar.types import LiveSessionType, FullBookingStatusGraphEnum


@strawberry.type
class ParticipantClassProgressType:
    live_session: LiveSessionType
    participant: ParticipantType
    host: AuthorizedType
    status: FullBookingStatusGraphEnum
    last_progress_date: datetime


def check_booking_status(
    booking: RawBooking,
    live_session: RawLiveSession,
) -> FullBookingStatusGraphEnum:
    """
    Determine the actual booking status, accounting for missed sessions.

    If a booking is still marked as 'BOOKED' but the session start time has passed,
    it should be considered 'MISSED'.

    Args:
        booking: The booking to check
        live_session: The live session associated with the booking

    Returns:
        The actual status of the booking
    """
    if (
        booking.status == BookingStatusEnum.BOOKED
        and live_session.meeting_start_time < pendulum.now("UTC")
    ):
        return FullBookingStatusGraphEnum.MISSED
    return FullBookingStatusGraphEnum(booking.status.value)


def _create_live_session_type(live_session: RawLiveSession) -> LiveSessionType:
    """Create a LiveSessionType from a RawLiveSession.

    Args:
        live_session: The raw live session data

    Returns:
        A LiveSessionType instance
    """
    return LiveSessionType(
        id=live_session.id,
        title=live_session.title or "",
        description=live_session.description or "",
        meeting_start_time=live_session.meeting_start_time,
        host_id=live_session.host_id,
        topic=live_session.topic,
        timezone=live_session.timezone,
        has_conflict=live_session.has_conflict,
        meeting_type=live_session.meeting_type,
        zoom_id=live_session.zoom_id or "",
        zoom_occurrence_id=live_session.zoom_occurrence_id or "",
        zoom_link=live_session.zoom_link or "",
        recording_url=live_session.recording_url,
        use_custom_meeting_link=live_session.use_custom_meeting_link or False,
        custom_meeting_link=live_session.custom_meeting_link,
        max_capacity=live_session.max_capacity,
        bookings_count=live_session.bookings_count,
        webinar_id=live_session.webinar_id,
    )


def _create_participant_type(participant: RawParticipant) -> ParticipantType:
    """Create a ParticipantType from a RawParticipant.

    Args:
        participant: The raw participant data

    Returns:
        A ParticipantType instance
    """
    return ParticipantType(
        id=participant.id,
        email=participant.email,
        first_name=participant.first_name,
        last_name=participant.last_name,
        chat_identity=participant.chat_identity,
        group_id=participant.group_id,
        member_id=participant.member_id,
        status=participant.status,
        cognito_sub=participant.cognito_sub,
        medical_record=participant.medical_record,
        is_test=participant.is_test,
        last_reset=participant.last_reset,
        created_at=participant.created_at,
        updated_at=participant.updated_at,
        activities=[],  # Activities are loaded separately when needed
    )


def _create_authorized_type(host: RawAuthorized) -> AuthorizedType:
    """Create an AuthorizedType from a RawAuthorized.

    Args:
        host: The raw authorized user data

    Returns:
        An AuthorizedType instance
    """
    return AuthorizedType(
        id=host.id,
        email=host.email,
        first_name=host.first_name,
        last_name=host.last_name,
        chat_identity=host.chat_identity,
        role=host.role,
        status=host.status,
        cognito_sub=host.cognito_sub,
        is_test=host.is_test,
        api_id=host.api_id,
        support_in_chat=host.support_in_chat,
        classes_admin=host.classes_admin,
        content_admin=host.content_admin or False,
        created_at=host.created_at,
        updated_at=host.updated_at,
    )


def _create_participant_class_progress_type(
    class_progress: ParticipantClassProgress,
) -> ParticipantClassProgressType:
    """Create a ParticipantClassProgressType from a ParticipantClassProgress.

    Args:
        class_progress: The class progress data

    Returns:
        A ParticipantClassProgressType instance
    """
    return ParticipantClassProgressType(
        live_session=_create_live_session_type(class_progress.live_session),
        participant=_create_participant_type(class_progress.participant),
        host=_create_authorized_type(class_progress.host),
        status=check_booking_status(
            booking=class_progress.booking,
            live_session=class_progress.live_session,
        ),
        last_progress_date=class_progress.booking.updated_at,
    )


async def get_participant_classes_progress(
    participant_id: UUID,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
) -> list[ParticipantClassProgressType] | None:
    """
    Retrieve class progress for a participant within an optional date range.

    This function fetches all class bookings for a participant and transforms them
    into GraphQL-compatible types. It also determines the actual booking status,
    marking sessions as 'MISSED' if they were booked but the session time has passed.

    Args:
        participant_id: The UUID of the participant
        start_date: Optional start date filter (inclusive). Only sessions starting
                   on or after this date will be included.
        end_date: Optional end date filter (exclusive). Only sessions starting
                 before this date will be included.

    Returns:
        A list of ParticipantClassProgressType objects representing the participant's
        class progress, ordered by most recently updated bookings first.

    Raises:
        ParticipantNotFoundError: If no participant exists with the given ID
        ValueError: If start_date is not before end_date
    """
    # Validate inputs
    validate_date_range(start_date, end_date)

    # Check if participant exists
    participant_exists = await Participant.filter(id=participant_id).exists()
    if not participant_exists:
        logger.warning(f"Participant not found: {participant_id}")
        raise ParticipantNotFoundError(
            f"Participant with ID {participant_id} not found"
        )

    try:
        class_progresses = (
            await ParticipantRepository.get_participant_classes_progress(
                participant_id=participant_id,
                start_date=start_date,
                end_date=end_date,
            )
        )

        # Transform data to GraphQL types
        result = [
            _create_participant_class_progress_type(class_progress)
            for class_progress in class_progresses
        ]

        return result

    except BaseORMException as e:
        logger.exception(e)
        raise DB_READ_ERROR

    except Exception as e:
        logger.error(
            f"Error retrieving class progress for participant {participant_id}: {str(e)}"
        )
        return None
