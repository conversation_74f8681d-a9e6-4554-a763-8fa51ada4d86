from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry
from src.log.logging import logger
from tortoise.expressions import Q
from tortoise.exceptions import BaseORMException
from src.content_library.messages import DB_READ_ERROR

from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.activity.models import (
    ParticipantActivityEnum,
    ParticipantActivity,
)
from ciba_participant.cohort.models import CohortProgramModules

from src.program.types import ProgramModuleSectionType


@strawberry.type
class SectionWithActivity:
    section: ProgramModuleSectionType
    completed: bool
    activityDate: Optional[datetime] = None


@strawberry.type()
class ProgressType:
    completed: bool
    activity_date: Optional[datetime] = None


@strawberry.type
class ParticipantModuleProgressType:
    first_weight: Optional[float] = None
    last_weight: Optional[float] = None
    average_activity_minutes: Optional[int] = None
    sections: list[SectionWithActivity]
    class_progress: ProgressType
    chat_progress: ProgressType
    success: bool = True
    error: Optional[str] = None


async def get_participant_module_progress(
    participant_id: UUID,
    cohort_module_id: UUID,
) -> ParticipantModuleProgressType | None:
    """
    Retrieve the progress of a participant in a specific cohort program module, including
    activity completion status, weight information, and average activity minutes.

    This function fetches the participant's activities within a cohort program module,
    including activities related to module sections and time-based activities, such as
    weight, activity and live session activity. It returns a summary of the participant's progress
    in the module, including fesection completion status, first and last weight, and average
    activity minutes.

    Args:
        participant_id (UUID): The ID of the participant.
        cohort_module_id (UUID): The ID of the cohort program module.

    Returns:
        ParticipantModuleProgressType | None: The participant's progress in the module,
        or None if an error occurs or module not found.

    Raises:
        Exception: If the module is not found or any unexpected error occurs during the process.
    """
    try:
        module = await CohortProgramModules.get_or_none(
            id=cohort_module_id
        ).prefetch_related(
            "program_module__sections",
        )

        module_section_ids = [
            section.id for section in module.program_module.sections
        ]

        activities = (
            await ParticipantActivity.filter(participant_id=participant_id)
            .filter(
                Q(
                    section_id__in=module_section_ids
                )  # Activity is in module section
                | (
                    Q(created_at__gte=module.started_at)
                    & Q(created_at__lt=module.ended_at)
                    & Q(section_id=None)
                )  # Activity is in module time range and has no section
            )
            .all()
            .order_by("created_at")
        )

        (
            section_progresses,
            class_activities,
            chat_activities,
        ) = await ParticipantActivityRepository.get_participant_module_progress(
            module=module,
            activities=activities,
            include_milestone_activities=True,
        )

        # Get first weight and last_weight
        first_weight: Optional[float] = None
        last_weight: Optional[float] = None

        if weight_activities := next(
            (
                section.activities
                for section in section_progresses
                if section.section.activity_type
                == ParticipantActivityEnum.WEIGHT
            ),
            None,
        ):
            first_weight = float(weight_activities[0].value)
            last_weight = (
                float(weight_activities[-1].value)
                if len(weight_activities) > 1
                else None
            )

        # Calculate activity
        average_activity_minutes: Optional[int] = None

        if activity_activities := next(
            (
                section.activities
                for section in section_progresses
                if section.section.activity_type
                == ParticipantActivityEnum.ACTIVITY
            ),
            None,
        ):
            average_activity_minutes = sum(
                float(activity.value) for activity in activity_activities
            ) // len(activity_activities)

        sections: list[SectionWithActivity] = []
        for section_progress in section_progresses:
            section = section_progress.section
            activities = section_progress.activities
            completed = len(activities) > 0
            sections.append(
                SectionWithActivity(
                    section=section,
                    completed=completed,
                    activityDate=(
                        activities[0].created_at  # Use first activity date
                        if completed
                        else None
                    ),
                )
            )

        class_progress = ProgressType(
            completed=len(class_activities) > 0,
            activity_date=(
                class_activities[0].created_at if class_activities else None
            ),
        )
        chat_progress = ProgressType(
            completed=len(chat_activities) > 0,
            activity_date=(
                chat_activities[0].created_at if chat_activities else None
            ),
        )

        return ParticipantModuleProgressType(
            first_weight=first_weight,
            last_weight=last_weight,
            average_activity_minutes=average_activity_minutes,
            sections=sections,
            class_progress=class_progress,
            chat_progress=chat_progress,
        )

    except BaseORMException as e:
        logger.exception(e)
        return ParticipantModuleProgressType(
            sections=[],
            class_progress=ProgressType(completed=False),
            chat_progress=ProgressType(completed=False),
            success=False,
            error=DB_READ_ERROR,
        )
    except Exception as e:
        logger.exception(e)
        return ParticipantModuleProgressType(
            sections=[],
            class_progress=ProgressType(completed=False),
            chat_progress=ProgressType(completed=False),
            success=False,
            error=str(e),
        )
