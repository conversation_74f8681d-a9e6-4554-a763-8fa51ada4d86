from uuid import UUID

import strawberry
from src.log.logging import logger
from ciba_participant.activity.crud import ParticipantActivityRepository
from ciba_participant.activity.models import ParticipantActivity
from ciba_participant.cohort.models import CohortProgramModules
from src.program.types import ProgramModuleType


@strawberry.type
class ParticipantProgramProgressType:
    program_module: ProgramModuleType
    total_activities: int
    completed_activities: int


async def get_participant_program_progress(
    participant_id: UUID, cohort_id: UUID
) -> list[ParticipantProgramProgressType] | None:
    try:
        modules = (
            await CohortProgramModules.filter(cohort_id=cohort_id)
            .prefetch_related(
                "program_module__sections",
            )
            .all()
            .order_by("started_at")
        )

        activities = (
            await ParticipantActivity.filter(participant_id=participant_id)
            .all()
            .order_by("created_at")
        )

        progress_per_module: list[ParticipantProgramProgressType] = []
        for module in modules:
            module_section_ids = [
                section.id for section in module.program_module.sections
            ]

            filtered_activities = [
                activity
                for activity in activities
                if (
                    # Check if activity is in module section
                    (activity.section_id in module_section_ids)
                    or
                    # Check if activity is in module time range and has no section
                    (
                        module.started_at
                        <= activity.created_at
                        < module.ended_at
                        and activity.section_id is None
                    )
                )
            ]

            (
                module_progress,
                class_activities,
                chat_activities,
            ) = await ParticipantActivityRepository.get_participant_module_progress(
                module=module,
                activities=filtered_activities,
                include_milestone_activities=True,
            )

            program_module_type = ProgramModuleType(
                id=module.program_module.id,
                created_at=module.program_module.created_at,
                updated_at=module.program_module.updated_at,
                title=module.program_module.title,
                short_title=module.program_module.short_title,
                length=module.program_module.length,
                description=module.program_module.description,
                program_id=module.program_module.program_id,
                order=module.program_module.order,
                started_at=module.started_at,
                ended_at=module.ended_at,
                cohort_module_id=module.id,
            )

            total_activities = (
                len(module_progress) + 2
            )  # Total activities: module sections + 1 Chat activity + 1 Class activity
            completed_activities = (
                sum(1 for activity in module_progress if activity.activities)
                + (1 if class_activities else 0)
                + (1 if chat_activities else 0)
            )

            progress_per_module.append(
                ParticipantProgramProgressType(
                    program_module=program_module_type,
                    total_activities=total_activities,
                    completed_activities=completed_activities,
                )
            )

        return progress_per_module

    except Exception as e:
        logger.exception(e)
        return None
