from datetime import datetime

import strawberry
from uuid import UUID
import pandas as pd
from src.log.logging import logger
from typing import Optional

from ciba_participant.activity.crud import (
    ParticipantActivityRepository,
    ParticipantActivity,
)
from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.cohort.crud import (
    CohortMembersRepository,
    CohortMemberOutput,
)

from src.participant.helpers.weight_activities import (
    get_weight_history_activities,
    ResampleFreqEnum,
)
from src.participant.types import WeightHistoryItem


@strawberry.type
class ActivityHistoryItem:
    week: int
    date: datetime
    physical_activity_minutes: float


@strawberry.type
class ParticipantActivityStats:
    last_activity: Optional[datetime] = None
    total_modules: int
    completed_modules_count: int
    total_activities: int
    completed_activities_count: int
    activity_history: list[ActivityHistoryItem]
    weight_history: list[WeightHistoryItem]


async def get_participant_activity_stats(
    participant_id: UUID,
) -> ParticipantActivityStats | None:
    try:
        member: (
            CohortMemberOutput | None
        ) = await CohortMembersRepository.get_cohort_member(participant_id)

        if member is None or member.cohort_id is None:
            raise Exception("Cohort Member not found")

        modules = (
            await CohortProgramModules.filter(cohort_id=member.cohort_id)
            .prefetch_related(
                "program_module__sections",
            )
            .all()
            .order_by("started_at")
        )

        activities = (
            await ParticipantActivity.filter(participant_id=participant_id)
            .all()
            .order_by("created_at")
        )

        completed_modules_count = 0
        completed_activities_count = 0
        total_activities_count = 0
        weight_activities: list[ParticipantActivity] = []
        physical_activities: list[ParticipantActivity] = []

        for module in modules:
            module_section_ids = [
                section.id for section in module.program_module.sections
            ]

            module_activities_count = (
                len(module_section_ids) + 2
            )  # Total activities: module sections + 1 Chat activity + 1 Class activity
            total_activities_count += module_activities_count

            module_activities = [
                activity
                for activity in activities
                if (
                    # Check if activity is in module section
                    (activity.section_id in module_section_ids)
                    or
                    # Check if activity is in module time range and has no section
                    (
                        module.started_at
                        <= activity.created_at
                        < module.ended_at
                        and activity.section_id is None
                    )
                )
            ]

            (
                module_progress,
                class_activities,
                chat_activities,
            ) = await ParticipantActivityRepository.get_participant_module_progress(
                module=module,
                activities=module_activities,
                include_milestone_activities=True,
            )

            completed_activities = (
                sum(1 for activity in module_progress if activity.activities)
                + (1 if class_activities else 0)
                + (1 if chat_activities else 0)
            )

            if (
                module_activities_count > 0
                and module_activities_count == completed_activities
                and class_activities
                and chat_activities
            ):
                completed_modules_count += 1

            completed_activities_count += completed_activities

            module_weight_activities = [
                activity
                for activity in activities
                if (
                    # Check if activity is in module section
                    (
                        (activity.section_id in module_section_ids)
                        and activity.activity_type
                        == ParticipantActivityEnum.WEIGHT
                    )
                    or
                    # Check if activity is in module time range and has no section
                    (
                        module.started_at
                        <= activity.created_at
                        < module.ended_at
                        and activity.section_id is None
                    )
                )
            ]

            if len(module_weight_activities) > 0:
                weight_activities = (
                    weight_activities + module_weight_activities
                )

            module_physical_activities = [
                activity
                for activity in activities
                if (
                    (activity.section_id in module_section_ids)
                    and activity.activity_type
                    == ParticipantActivityEnum.ACTIVITY
                )
            ]

            if len(module_physical_activities) > 0:
                physical_activities = (
                    physical_activities + module_physical_activities
                )

        weight_history = get_weight_history_activities(
            weight_activities, resample_freq=ResampleFreqEnum.NONE
        )
        activity_history: list[ActivityHistoryItem] = []

        def to_activity_item(activity: ParticipantActivity):
            try:
                minutes = float(activity.value)
            except ValueError:
                minutes = 0

            return ActivityHistoryItem(
                date=activity.created_at,
                physical_activity_minutes=minutes,
                week=None,
            )

        activity_items = map(to_activity_item, physical_activities)

        act_df = pd.DataFrame(
            activity_items, columns=["date", "physical_activity_minutes"]
        ).sort_values(by="date", ascending=True)
        act_df = act_df.set_index("date")

        first_activity_date = act_df.index.min()

        if len(act_df) > 0:
            act_df = (
                act_df.resample("W")
                .sum()
                .dropna()
                .sort_index(ascending=True)
                .reset_index()
                .rename_axis("week")
                .reset_index()
            )

            for i, row in act_df.iterrows():
                if i == 0:
                    # Send non-resampled date for earliest activity,
                    # as it is used in the FE for displaying activity start
                    activity_history.append(
                        ActivityHistoryItem(
                            date=first_activity_date,
                            physical_activity_minutes=row[
                                "physical_activity_minutes"
                            ],
                            week=row["week"],
                        )
                    )
                else:
                    activity_history.append(
                        ActivityHistoryItem(
                            date=row["date"],
                            physical_activity_minutes=row[
                                "physical_activity_minutes"
                            ],
                            week=row["week"],
                        )
                    )

        last_activity = None
        if len(activities) > 0:
            last_activity = activities[-1].created_at

        return ParticipantActivityStats(
            total_modules=len(modules),
            total_activities=total_activities_count,
            completed_modules_count=completed_modules_count,
            completed_activities_count=completed_activities_count,
            last_activity=last_activity,
            weight_history=weight_history,
            activity_history=activity_history,
        )

    except Exception as e:
        logger.exception(e)
        return None
