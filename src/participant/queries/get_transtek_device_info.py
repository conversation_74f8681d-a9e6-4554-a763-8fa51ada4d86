from uuid import UUID


from ciba_participant.rpm_api.api import (
    get_transtek_device_info as get_transtek_device,
)
from src.transtek.types import TranstekDeviceInfoType


async def get_transtek_device_info(
    participant_id: UUID,
) -> TranstekDeviceInfoType:
    """Get transtek device info."""
    device = await get_transtek_device(participant_id)

    return TranstekDeviceInfoType.from_model(device)
