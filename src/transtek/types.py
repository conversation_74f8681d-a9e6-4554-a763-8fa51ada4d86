from typing import Optional

import strawberry

from ciba_participant.rpm_api.models import TranstekDeviceInfo


@strawberry.type
class TranstekDeviceInfoType:
    """Strawberry type for Transtek device information."""

    id: str
    serial_number: str
    imei: str
    model: str
    device_type: str
    status: str

    # Optional fields
    tracking_number: Optional[str] = None
    carrier: Optional[str] = None
    tracking_url: Optional[str] = None
    timezone: Optional[str] = None
    last_status_report: Optional[str] = None
    member_id: Optional[str] = None

    @classmethod
    def from_model(cls, model: TranstekDeviceInfo) -> "TranstekDeviceInfoType":
        """Convert Pydantic model to Strawberry type."""
        return cls(
            id=model.id,
            serial_number=model.device_id,
            imei=model.imei,
            model=model.model,
            device_type=model.device_type,
            status=model.status,
            tracking_number=model.tracking_number,
            carrier=model.carrier,
            tracking_url=model.tracking_url,
            timezone=model.timezone,
            last_status_report=str(model.last_status_report)
            if model.last_status_report
            else None,
            member_id=model.member_id,
        )
