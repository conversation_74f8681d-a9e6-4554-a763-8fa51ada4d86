import strawberry

from ciba_participant.rpm_api.api import (
    get_carrier_list as get_carrier_list_rpm,
)


@strawberry.type
class CarrierData:
    name: str
    tracking_link_template: str


async def get_carrier_list() -> list[CarrierData]:
    """
    Returns a list of carriers and their tracking link templates.
    """
    carriers = await get_carrier_list_rpm()

    return [
        CarrierData(
            name=name.upper(),
            tracking_link_template=link_template,
        )
        for name, link_template in carriers.items()
    ]
