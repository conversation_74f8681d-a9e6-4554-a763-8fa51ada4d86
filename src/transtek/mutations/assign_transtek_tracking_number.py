from typing import Optional

from ciba_participant.log.logging import logger
from ciba_participant.rpm_api.api import update_transtek_tracking_data
from ciba_participant.rpm_api.exceptions import RPMCallError
from ciba_participant.rpm_api.models import TranstekTrackingData
from src.common.types import DetailedResponse


async def assign_transtek_tracking_number(
    tracking_number: str,
    carrier: str,
    imei: Optional[str] = None,
    serial_number: Optional[str] = None,
) -> DetailedResponse:
    """Add tracking number and tracking info to transtek scale."""
    try:
        await update_transtek_tracking_data(
            imei=imei,
            serial_number=serial_number,
            tracking_data=TranstekTrackingData(
                tracking_number=tracking_number, carrier=carrier
            ),
        )

    except RPMCallError as e:
        logger.exception(f"Error assigning tracking number: {e}")
        return DetailedResponse(success=False, message=str(e))

    except Exception as e:
        logger.exception(f"Unexpected error assigning tracking number: {e}")
        return DetailedResponse(success=False, message="Unexpected error")

    return DetailedResponse(success=True, message="Tracking number assigned")
