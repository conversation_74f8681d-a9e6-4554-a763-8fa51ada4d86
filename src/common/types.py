from typing import Optional

import strawberry


@strawberry.type
class PageInfoType:
    current_page: int
    has_next_page: bool
    has_previous_page: bool
    last_page: int
    per_page: int
    total: int


@strawberry.type
class SimpleRespType:
    status: bool


@strawberry.type
class ShortResponse:
    success: bool
    error: Optional[str] = None


@strawberry.type
class DetailedResponse:
    success: bool
    message: Optional[str] = None
    error_code: Optional[str] = None
