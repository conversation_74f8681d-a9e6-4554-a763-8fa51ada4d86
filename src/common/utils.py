import inspect
from dataclasses import dataclass

import strawberry


@dataclass
class InfoParam:
    name: str
    already_existed: bool


def ensure_info_param(fn):
    """
    The only way to get information from the request is to use the `info` parameter
    or to use the `strawberry.Info` type hint.

    There is not other way to get the information from the request.
    So if we for example need to create a decorator that needs to access the request `info`,
    we would need to make sure that the `info` parameter is present in the function
    signature.

    This function makes sure that the `info` parameter is present in the function signature.

    A decorator can use this function to make sure that the `info` parameter is present.
    And if it is not present, it will add it to the function signature.

    This functions returns an `InfoParam` object that contains the name of the parameter
    that was found or added and a flag that indicates if the parameter was already present
    or if it was added by this function.

    You can remove the `info` parameter from the function signature again later
    to call the original function as if the `info` parameter was never there.
    """
    parameters = []
    extra_params = []

    signature = inspect.signature(fn, follow_wrapped=True)

    for p in signature.parameters.values():
        if p.kind <= inspect.Parameter.KEYWORD_ONLY:
            parameters.append(p)
        else:
            extra_params.append(p)

    info_param: InfoParam | None = None

    for param in signature.parameters.values():
        if param.annotation is strawberry.Info or (
            param.name == "info"
            and param.annotation == inspect.Parameter.empty
        ):
            info_param = InfoParam(name=param.name, already_existed=True)
            break

    if not info_param:
        info_param = InfoParam(name="_info", already_existed=False)

        parameters.append(
            inspect.Parameter(
                name="_info",
                annotation=strawberry.Info,
                kind=inspect.Parameter.KEYWORD_ONLY,
            ),
        )

        parameters.extend(extra_params)
        fn.__signature__ = signature.replace(parameters=parameters)

    return info_param
