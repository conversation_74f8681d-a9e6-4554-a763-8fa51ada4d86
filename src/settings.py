from functools import lru_cache
from typing import Any
from urllib.parse import quote_plus

from ciba_participant.settings import ENV
from ciba_participant.settings import Settings as CibaSettings


class Settings(CibaSettings):
    PROJECT_NAME: str = "CibaHealth participant-admin API"
    DEBUG: int = 1
    VERSION: str = "3.2.2"
    ENV: str = ENV.LOCAL
    DEV_EMAIL: str = "<EMAIL>"

    SOLERA_CLIENT_ID: str = ""
    SOLERA_CLIENT_SECRET: str = ""
    AWS_BUCKET_NAME: str = ""

    REDIS_PORT: int = 6379
    REDIS_HOST: str = "localhost"
    ADMIN_SENTRY_DSN: str = ""

    PROVIDERS_COGNITO_APP_CLIENT_ID: str = ""
    PROVIDERS_COGNITO_USER_POOL_ID: str = ""

    @property
    def default_db_url(self) -> str:
        """Construct default database url."""
        return (
            f"postgres://"
            f"{self.POSTGRES_USER}:{quote_plus(self.POSTGRES_PASSWORD)}"
            f"@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}"
            f"/{self.POSTGRES_DB}"
        )


@lru_cache()
def get_settings(**kwargs: Any) -> Settings:
    """Initialize settings."""
    settings = Settings(**kwargs)
    return settings
