from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.crud.get_content_material import (
    MaterialFilters as MaterialQueryFilters,
)
from ciba_participant.content_library.models import ContentMaterial
from ciba_participant.program.crud import ProgramRepository
from tortoise.exceptions import BaseORMException

from src.content_library.exceptions import MaterialQueryException
from src.content_library.inputs import MaterialFilters
from src.content_library.types import (
    ContentMaterialList,
    ContentMaterialElement,
    RelatedProgram,
)
from src.log.logging import logger


async def get_content(
    page: int = 1, per_page: int = 10, filters: MaterialFilters = None
) -> ContentMaterialList:
    """
    Method to retrieve the content from library with the specified filters.
    """
    query_filters = (
        MaterialQueryFilters(**vars(filters)) if filters else filters
    )

    try:
        result = await ContentMaterialRepository.get_material(
            page,
            per_page,
            query_filters,
        )

        programs_map = await ProgramRepository.get_all_programs_names_map()

        return ContentMaterialList(
            success=True,
            total=result.total,
            total_pages=result.total_pages,
            items=[
                map_to_response(entity, programs_map)
                for entity in result.items
            ],
        )
    except BaseORMException as error:
        logger.exception(error)
        raise MaterialQueryException(
            "An error occurred while reading the requested content."
        )


def map_to_response(
    entity: ContentMaterial, programs_map: dict
) -> ContentMaterialElement:
    """
    Method to map a content material db entity to a content material element.
    """
    programs = [
        RelatedProgram(
            id=program.program_id, title=programs_map.get(program.program_id)
        )
        for program in entity.programs
    ]
    tags = [content_tag.tag for content_tag in entity.tags]
    activity_types = [
        activity.activity_type for activity in entity.activity_types
    ]

    return ContentMaterialElement(
        id=entity.id,
        added_at=entity.created_at,
        mime_type=entity.mime_type,
        title=entity.title,
        description=entity.description,
        status=entity.status,
        link=entity.link,
        link_expiration=entity.file_url_expiration,
        tags=tags,
        activity_types=activity_types,
        programs=programs,
        file_name=entity.file_name,
        file_size=entity.file_size,
        form_id=entity.form_id,
    )
