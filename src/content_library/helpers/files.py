from json import dumps

from botocore.exceptions import BotoCoreError
from ciba_participant import get_settings

from ciba_participant.common.aws_handler import generate_presigned_url

from src.content_library.exceptions import MaterialStorageException
from src.log.logging import logger

settings = get_settings()
EXPIRATION = 3600


def generate_upload_link(file_name: str, mime_type: str) -> (str, str):
    """
    Method to generate a link to upload a content material file.
    """
    logger.info(
        "Generating upload link for file %s on bucket %s",
        file_name,
        settings.CONTENT_LIBRARY_BUCKET_NAME,
    )

    try:
        url_generation = generate_presigned_url(
            bucket_name=settings.CONTENT_LIBRARY_BUCKET_NAME,
            object_name=file_name,
            content_type=mime_type,
            expiration=EXPIRATION,
            region_name=settings.AWS_REGION,
        )

        return url_generation.get("url"), dumps(url_generation["fields"])
    except BotoCoreError as error:
        logger.exception(error)
        raise MaterialStorageException(
            "An error occurred during the generation of upload link."
        )


def get_storage_path(mime_type: str) -> str:
    """
    Method to get the storage path based on the mime type.
    """
    if not mime_type:
        raise TypeError("Mime type not specified")

    if mime_type == "application/pdf":
        return "pdf"

    if mime_type.startswith("video/"):
        return "video"

    raise TypeError(f"Mime type {mime_type} not supported for files.")
