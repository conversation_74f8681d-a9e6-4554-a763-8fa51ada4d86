from uuid import UUID

from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.exceptions import (
    ContentMaterialNotFoundError,
    ContentMaterialForbiddenError,
)
from tortoise.exceptions import BaseORMException

from src.content_library.messages import NOT_FOUND, DB_ERROR, UNAUT<PERSON>ORIZED
from src.content_library.types import MaterialSimpleResponse
from src.log.logging import logger


async def update_material_status(
    material_id: UUID, autor_id: UUID
) -> MaterialSimpleResponse:
    """
    Method that handles the status update for a material.
    :param material_id: ID of the content material to update
    :param autor_id: ID of the user that archived or unarchived the content material
    :return: MaterialSimpleResponse
    """
    try:
        await ContentMaterialRepository.update_archive_status(
            material_id, autor_id
        )

        return MaterialSimpleResponse(id=material_id, success=True)
    except ContentMaterialForbiddenError as error:
        logger.error(error)
        return MaterialSimpleResponse(success=False, error=UNAUTHORIZED)
    except ContentMaterialNotFoundError as error:
        logger.error(error)
        return MaterialSimpleResponse(success=False, error=NOT_FOUND)
    except BaseORMException as error:
        logger.error(error)
        return MaterialSimpleResponse(success=False, error=DB_ERROR)
