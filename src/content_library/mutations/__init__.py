from uuid import UUID

import strawberry
from strawberry import Info

from src.content_library.exceptions import MaterialStorageException
from src.content_library.inputs import MaterialData
from src.content_library.mutations.add_content import add_new_content
from src.content_library.mutations.delete_content import delete_content
from src.content_library.mutations.edit_content import edit_content
from src.content_library.mutations.update_archive_status import (
    update_material_status,
)
from src.content_library.types import (
    MaterialCreationResponse,
    MaterialSimpleResponse,
)
from src.log.logging import logger


async def add_content_material(
    info: Info, material_data: MaterialData
) -> MaterialCreationResponse:
    """
    Resolver to handle content material creation.
    """
    logger.info("Adding new content material %s", material_data)
    try:
        return await add_new_content(info.context.user.id, material_data)
    except (
        MaterialStorageException,
        TypeError,
        ValueError,
    ) as error:
        return MaterialCreationResponse(
            success=False,
            error=str(error),
        )


async def delete_content_material(
    info: Info,
    material_id: UUID,
) -> MaterialSimpleResponse:
    """
    Resolver to handle material deletion.
    """
    logger.info(f"Deleting material {material_id}")
    return await delete_content(info=info, material_id=material_id)


async def edit_content_material(
    info: Info,
    material_id: UUID,
    material_data: MaterialData,
) -> MaterialCreationResponse:
    """
    Resolver to handle content material edition.
    """
    logger.info(f"Editing material {material_id} with data {material_data}")

    return await edit_content(material_id, info.context.user.id, material_data)


async def update_archive_status(
    info: Info,
    material_id: UUID,
) -> MaterialSimpleResponse:
    """
    Resolver to handle archived or active status update.
    """
    logger.info(f"Updating archive status for material {material_id}")

    return await update_material_status(material_id, info.context.user.id)


@strawberry.type()
class ContentLibraryMutation:
    """Content Library mutations."""

    add_content_material: MaterialCreationResponse = strawberry.field(
        resolver=add_content_material
    )
    delete_content_material: MaterialSimpleResponse = strawberry.field(
        resolver=delete_content_material
    )
    edit_content_material: MaterialCreationResponse = strawberry.field(
        resolver=edit_content_material
    )
    update_archive_status: MaterialSimpleResponse = strawberry.field(
        resolver=update_archive_status
    )
