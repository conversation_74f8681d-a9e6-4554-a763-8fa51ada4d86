from uuid import UUID

from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.crud.create_content_material import (
    NewMaterialData,
)
from tortoise.exceptions import BaseORMException

from ciba_participant.content_library.exceptions import (
    ContentMaterialNotFoundError,
    ContentMaterialForbiddenError,
)

from ciba_participant.content_library.helpers import sanitize_file_name
from src.content_library.exceptions import MaterialStorageException
from src.content_library.helpers.files import (
    get_storage_path,
    generate_upload_link,
)
from src.content_library.inputs import MaterialData
from src.content_library.messages import DB_ERROR, NOT_FOUND, UNAUTHORIZED
from src.content_library.mutations.add_content import validate_request
from src.content_library.types import MaterialCreationResponse
from src.log.logging import logger


async def edit_content(
    material_id: UUID, author_id: UUID, material_data: MaterialData
) -> MaterialCreationResponse:
    """
    Method to handle a content material edition.
    """
    validate_request(material_data)

    file_location = (
        get_storage_path(material_data.mime_type)
        if material_data.file_size
        else None
    )

    try:
        process_response = await ContentMaterialRepository.edit_material(
            material_id,
            NewMaterialData(
                **vars(material_data), file_location=file_location
            ),
            author_id,
        )

        response = MaterialCreationResponse(
            success=True,
            id=process_response.material_id,
        )

        if process_response.needs_file_upload:
            file_name = f"{file_location}/{material_id}-{sanitize_file_name(material_data.file_name)}"
            upload_link, fields = generate_upload_link(
                file_name, material_data.mime_type
            )
            response.upload_url = upload_link
            response.fields = fields

        return response
    except ContentMaterialForbiddenError as error:
        logger.error(error)
        return MaterialCreationResponse(success=False, error=UNAUTHORIZED)
    except ContentMaterialNotFoundError:
        logger.error(f"Material {material_id} not found.")
        return MaterialCreationResponse(success=False, error=NOT_FOUND)
    except BaseORMException as error:
        logger.error(error)
        return MaterialCreationResponse(
            success=False,
            error=DB_ERROR,
        )
    except MaterialStorageException as error:
        logger.error(error)
        return MaterialCreationResponse(
            success=False,
            error=str(error),
        )
