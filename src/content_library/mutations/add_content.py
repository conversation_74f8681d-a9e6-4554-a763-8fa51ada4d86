from uuid import UUID

from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.crud.create_content_material import (
    NewMaterialData,
)
from ciba_participant.content_library.exceptions import (
    ContentMaterialForbiddenError,
)
from tortoise.exceptions import BaseORMException

from ciba_participant.content_library.helpers import sanitize_file_name
from src.content_library.exceptions import MaterialStorageException
from src.content_library.helpers.files import (
    get_storage_path,
    generate_upload_link,
)
from src.content_library.inputs import MaterialData
from src.content_library.messages import (
    DB_ERROR,
    DESCRIPTION_EMPTY,
    DESCRIPTION_TOO_LONG,
    TITLE_EMPTY,
    TITLE_TOO_LONG,
    UNAUTHORIZED,
)
from src.content_library.types import MaterialCreationResponse
from src.log.logging import logger


async def add_new_content(
    author_id: UUID, material_data: MaterialData
) -> MaterialCreationResponse:
    """
    Method to add a new content to the library.
    """
    validate_request(material_data)

    file_location = (
        get_storage_path(material_data.mime_type)
        if material_data.file_size
        else None
    )

    try:
        material_id = await ContentMaterialRepository.add_new_material(
            NewMaterialData(
                **vars(material_data), file_location=file_location
            ),
            author_id,
        )
    except BaseORMException as error:
        logger.error(error)
        raise MaterialStorageException(DB_ERROR)
    except ContentMaterialForbiddenError as error:
        logger.error(error)
        return MaterialCreationResponse(success=False, error=UNAUTHORIZED)

    response = MaterialCreationResponse(
        success=True,
        id=material_id,
    )

    if material_data.file_size:
        file_name = f"{file_location}/{material_id}-{sanitize_file_name(material_data.file_name)}"
        upload_link, fields = generate_upload_link(
            file_name, material_data.mime_type
        )
        response.upload_url = upload_link
        response.fields = fields

    return response


def validate_request(material_data: MaterialData):
    """
    Method to validate the request to add a new content to the library.
    """
    validate_title_and_description(material_data)

    if not all(
        [
            material_data.tags,
            material_data.activity_types,
            material_data.programs,
        ]
    ):
        raise ValueError("Tags, activity types and programs cannot be empty.")

    if material_data.form_id and material_data.content_url:
        raise ValueError("Cannot send form id and content url together.")

    if material_data.form_id and (
        material_data.file_name or material_data.file_size
    ):
        raise ValueError("Cannot send form id and file data together.")

    if material_data.content_url and (
        material_data.file_name or material_data.file_size
    ):
        raise ValueError("Cannot send content url and file data together.")

    validate_file_content(material_data)


def validate_file_content(material_data: MaterialData):
    """
    Method to validate the fields related with file based materia.
    """
    if not material_data.content_url and not material_data.form_id:
        if not material_data.file_name or not material_data.file_size:
            raise ValueError(
                "File data cannot be empty when content url or form id is empty."
            )


def validate_title_and_description(material_data: MaterialData):
    """
    Method to validate content material title and description fields.
    """
    if not material_data.title:
        raise ValueError(TITLE_EMPTY)
    if not material_data.description:
        raise ValueError(DESCRIPTION_EMPTY)
    if len(material_data.title) > 255:
        raise ValueError(TITLE_TOO_LONG)
    if len(material_data.description) > 500:
        raise ValueError(DESCRIPTION_TOO_LONG)
