from uuid import UUID

from strawberry import Info
from ciba_participant.common.aws_handler import S3DeleteError
from ciba_participant.content_library.crud import ContentMaterialRepository
from ciba_participant.content_library.exceptions import (
    ContentMaterialNotFoundError,
    ContentMaterialForbiddenError,
)

from src.content_library.messages import NOT_FOUND, UNAUTHORIZED
from src.content_library.types import MaterialSimpleResponse
from src.log.logging import logger


async def delete_content(
    info: Info,
    material_id: UUID,
) -> MaterialSimpleResponse:
    """
    Method for deleting content material.

    Args:
        info: GraphQL resolver info containing context data.
        material_id: UUID of the content material to delete.

    Returns:
        MaterialSimpleResponse: Response object indicating success or failure.

    Raises:
        No exceptions are raised as they are caught and returned as error responses.
    """
    author_id = info.context.user.id
    try:
        material_id = await ContentMaterialRepository.delete_material(
            content_material_id=material_id, user_id=author_id
        )
    except S3DeleteError as error:
        logger.error(error)
        return MaterialSimpleResponse(
            success=False,
            error="Failed to delete the material from storage. Please contact tech team.",
        )
    except ContentMaterialNotFoundError:
        logger.error(f"Material {material_id} not found.")
        return MaterialSimpleResponse(success=False, error=NOT_FOUND)
    except ContentMaterialForbiddenError as error:
        logger.error(error)
        return MaterialSimpleResponse(success=False, error=UNAUTHORIZED)
    except Exception as e:
        logger.error(e)
        return MaterialSimpleResponse(
            success=False,
            error="An error occurred while deleting the material.",
        )

    response = MaterialSimpleResponse(
        success=True,
    )

    return response
