from typing import Optional
from uuid import UUID

import strawberry


@strawberry.input
class CohortIDInput:
    id: UUID


# Define input types for CohortMembers
@strawberry.input
class CohortMemberCreateInput:
    cohort_id: UUID
    participant_id: UUID


@strawberry.input
class CohortMemberIDInput:
    cohort_id: UUID
    participant_id: UUID


@strawberry.input
class AdminInput:
    chat_identity: str
    id: UUID


@strawberry.input
class AdminsInput:
    admins: list[AdminInput]


@strawberry.input
class ScheduleIdsInput:
    schedule_ids: Optional[list[int]] = None
