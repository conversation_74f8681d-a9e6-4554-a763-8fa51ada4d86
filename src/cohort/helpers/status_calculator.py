import pendulum

from ciba_participant.cohort.models import CohortStatusEnum, FullCohort
from src.cohort.types import CohortStatusGraphEnum


def calculate_cohort_status(cohort: FullCohort) -> CohortStatusGraphEnum:
    now = pendulum.now()

    if cohort.status == CohortStatusEnum.COMPLETED:
        return CohortStatusGraphEnum.COMPLETED.value

    if cohort.started_at > now:
        return CohortStatusGraphEnum.PENDING.value

    end_date = cohort.end_date

    if end_date <= now.add(days=28):
        return CohortStatusGraphEnum.ENDING.value

    return CohortStatusGraphEnum.ACTIVE.value
