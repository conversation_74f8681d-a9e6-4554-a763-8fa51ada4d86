from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import (
    CohortRepository,
    CohortMembersRepository,
)
from src.log.logging import logger

from ciba_participant.cohort.models import Cohort
from ciba_participant.participant.models import Participant
from src.cohort.types import CohortType
from ciba_participant.chat_api.chat_api import (
    assign_participant_to_chat,
    remove_participant_from_chat,
)


@strawberry.input
class UpdateCohortInput:
    id: UUID
    name: Optional[str] = None
    start_date: Optional[datetime] = None
    limit: Optional[int] = None
    program_id: Optional[UUID] = None


async def update_cohort(info, data: UpdateCohortInput) -> CohortType:
    logger.info(f"Updating cohort {data.id}")

    await CohortRepository.update_cohort(
        data.id,
        name=data.name,
        start_date=data.start_date,
        limit=data.limit,
        program_id=data.program_id,
    )

    logger.info(f"Updated cohort {data.id}")

    cohort = await CohortRepository.get_cohort(data.id)

    if not cohort:
        raise Exception("Cohort not found")

    return CohortType(
        id=cohort.id,
        created_at=cohort.created_at,
        updated_at=cohort.updated_at,
        started_at=cohort.started_at,
        name=cohort.name,
        limit=cohort.limit,
        status=cohort.status,
        program_id=cohort.program_id,
    )


@strawberry.input
class MoveParticipantInput:
    participant_id: UUID
    old_cohort_id: UUID
    new_cohort_id: UUID


@strawberry.type
class MoveParticipantOutput:
    participant_id: UUID
    cohort_id: UUID


@strawberry.type
class WrongCohort:
    msg: str = "Participant program does not match cohort program"


async def move_participant(
    data: MoveParticipantInput,
) -> MoveParticipantOutput | WrongCohort:
    participant = await Participant.filter(
        id=data.participant_id
    ).get_or_none()

    if not participant:
        return WrongCohort(msg="Participant not found")

    old_cohort = (
        await Cohort.filter(id=data.old_cohort_id)
        .prefetch_related("program")
        .get_or_none()
    )
    new_cohort = (
        await Cohort.filter(id=data.new_cohort_id)
        .prefetch_related("program")
        .get_or_none()
    )

    if old_cohort.program.title != new_cohort.program.title:
        return WrongCohort(
            msg=f"Participant program {old_cohort.program.title} does not match cohort program"
        )

    await remove_participant_from_chat(
        group_unique_name=old_cohort.unique_name,
        participant_id=participant.id,
    )

    await CohortMembersRepository.delete_cohort_member(
        cohort_id=data.old_cohort_id, participant_id=data.participant_id
    )
    logger.info(
        f"Participant {data.participant_id} deleted from {old_cohort.name} "
    )

    await CohortMembersRepository.create_cohort_member(
        cohort_id=data.new_cohort_id, participant_id=data.participant_id
    )

    await assign_participant_to_chat(
        chat_identity=participant.chat_identity,
        group_unique_name=new_cohort.unique_name,
        participant_id=participant.id,
        participant_type="participant",
    )
    logger.info(
        f"Participant {data.participant_id} added to {new_cohort.name} "
    )

    return MoveParticipantOutput(
        participant_id=participant.id, cohort_id=data.new_cohort_id
    )
