from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import (
    CohortMembersRepository,
    CohortRepository,
)
from ciba_participant.cohort.models import CohortMembers
from ciba_participant.schedule_manager.service import ScheduleManager
from src.log.logging import logger

from src.cohort.inputs import (
    CohortIDInput,
    CohortMemberCreateInput,
    CohortMemberIDInput,
    ScheduleIdsInput,
)
from src.cohort.mutations.create_cohort import (
    CreateCohortOutput,
    create_cohort,
)
from src.cohort.mutations.update_cohort import (
    update_cohort,
    move_participant,
    MoveParticipantOutput,
    WrongCohort,
)
from src.cohort.mutations.end_cohort import end_cohort
from src.cohort.types import CohortMemberType, CohortType
from src.common.types import SimpleRespType
from src.settings import get_settings

settings = get_settings()


async def delete_cohort(data: CohortIDInput) -> SimpleRespType:
    cohort_members = await CohortMembers.filter(cohort_id=data.id).exists()
    if cohort_members:
        logger.error(
            "A cohort with at least one participant, cannot be deleted."
        )
        raise ValueError(
            "A cohort with at least one participant, cannot be deleted."
        )

    try:
        logger.info(f"Deleting cohort {data.id}")
        await CohortRepository.delete_cohort(data.id)
        logger.info(f"Deleted cohort {data.id}")
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        raise e


# Resolver functions for CohortMembers
async def create_cohort_member(
    info, data: CohortMemberCreateInput
) -> CohortMemberType:
    try:
        logger.info(
            f"Creating cohort {data.cohort_id} member {data.participant_id}"
        )
        cohort_member = await CohortMembersRepository.create_cohort_member(
            data.cohort_id, data.participant_id
        )
        logger.info(
            f"Created cohort {cohort_member.cohort_id} member {cohort_member.participant_id}"
        )

        cohort_loader = info.context["cohort_loader"]
        try:
            cohort_loader.clear(data.cohort_id)
        except KeyError:
            pass
        return cohort_member
    except Exception as e:
        logger.exception(e)
        raise e


async def update_cohort_member(
    info, data: CohortMemberIDInput, new_cohort_id: UUID
) -> CohortMemberType:
    try:
        logger.info(
            f"Updating cohort {data.cohort_id} member {data.participant_id} to {new_cohort_id}"
        )
        updated_cohort_member = (
            await CohortMembersRepository.update_cohort_member(
                data.participant_id, new_cohort_id
            )
        )
        logger.info(
            f"Updated cohort {updated_cohort_member.cohort_id} member {updated_cohort_member.participant_id}"
        )

        cohort_loader = info.context["cohort_loader"]
        try:
            cohort_loader.clear(data.cohort_id)
        except KeyError:
            pass
        return updated_cohort_member
    except Exception as e:
        logger.exception(e)
        raise e


async def delete_cohort_member(data: CohortMemberIDInput) -> SimpleRespType:
    try:
        logger.info(
            f"Deleting cohort {data.cohort_id} member {data.participant_id}"
        )
        await CohortMembersRepository.delete_cohort_member(
            data.cohort_id, data.participant_id
        )
        logger.info(
            f"Deleted cohort {data.cohort_id} member {data.participant_id}"
        )
        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


async def import_pending_cohorts(
    data: Optional[ScheduleIdsInput],
) -> SimpleRespType:
    try:
        schedule_manager = ScheduleManager(
            settings.SCHEDULE_MANAGER_API_ENDPOINT
        )
        schedule_ids = data.schedule_ids if data else None

        await schedule_manager.import_pending_cohorts(
            schedule_ids=schedule_ids
        )

        return SimpleRespType(status=True)
    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)


@strawberry.type
class CohortMutation:
    create_cohort: CreateCohortOutput = strawberry.field(
        resolver=create_cohort
    )
    update_cohort: CohortType = strawberry.field(resolver=update_cohort)
    delete_cohort: SimpleRespType = strawberry.field(resolver=delete_cohort)
    create_cohort_member: CohortMemberType = strawberry.field(
        resolver=create_cohort_member
    )
    update_cohort_member: CohortMemberType = strawberry.field(
        resolver=update_cohort_member
    )
    delete_cohort_member: SimpleRespType = strawberry.field(
        resolver=delete_cohort_member
    )
    import_pending_cohorts: SimpleRespType = strawberry.field(
        resolver=import_pending_cohorts
    )
    move_participant: MoveParticipantOutput | WrongCohort = strawberry.field(
        resolver=move_participant
    )
    end_cohort: SimpleRespType = strawberry.field(resolver=end_cohort)
