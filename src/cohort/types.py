from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.models import CohortStatusEnum
from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.participant.models import RawAuthorized, RawParticipant
from ciba_participant.program.models import RawProgram

from src.auth.types import AuthorizedType
from src.common.types import PageInfoType
from src.participant.types import ParticipantType
from src.program.types import ProgramType


@strawberry.enum
class CohortStatusGraphEnum(Enum):
    ACTIVE = CohortStatusEnum.ACTIVE.value
    COMPLETED = CohortStatusEnum.COMPLETED.value
    ENDING = "ending"
    PENDING = "pending"


@strawberry.type
class CohortMemberType:
    created_at: datetime
    updated_at: datetime
    cohort_id: UUID
    participant_id: UUID


@strawberry.type
class CohortMembersListType:
    cohort_members: list[CohortMemberType]


@strawberry.type
class CohortType:
    id: UUID
    created_at: datetime
    updated_at: datetime
    limit: Optional[int]
    status: Optional[CohortStatusGraphEnum]
    end_date: Optional[datetime] = None
    name: Optional[str]
    started_at: Optional[datetime]
    program_id: UUID

    _participants: strawberry.Private[Optional[list[RawParticipant]]] = None
    _created_by: strawberry.Private[Optional[RawAuthorized]] = None
    _program: strawberry.Private[Optional[RawProgram]] = None

    async def load_members(self):
        if self._participants is None:
            self._participants = (
                await CohortRepository.get_cohort_participants(self.id)
            )
        return self._participants

    async def load_created_by(self):
        if self._created_by is None:
            self._created_by = await CohortRepository.get_cohort_created_by(
                self.id
            )
        return self._created_by

    async def load_program(self):
        if self._program is None:
            self._program = await CohortRepository.get_cohort_program(self.id)
        return self._program

    @strawberry.field
    async def participants(self) -> list[ParticipantType]:
        participants = await self.load_members()

        participants_output = [
            ParticipantType(
                id=participant.id,
                created_at=participant.created_at,
                updated_at=participant.updated_at,
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
                group_id=participant.group_id,
                member_id=participant.member_id,
                status=participant.status,
                cognito_sub=participant.cognito_sub,
                medical_record=participant.medical_record,
                is_test=participant.is_test,
                last_reset=participant.last_reset,
                chat_identity=participant.chat_identity,
                activities=[],
            )
            for participant in participants
        ]

        return participants_output

    @strawberry.field
    async def is_editable(self, info) -> bool:
        members = await self.load_members()

        return len(members) == 0

    @strawberry.field
    async def hasParticipants(self, info) -> bool:
        members = await self.load_members()

        return len(members) != 0

    @strawberry.field
    async def created_by(self, info) -> Optional[AuthorizedType]:
        created_by = await self.load_created_by()

        if created_by is None:
            return None

        output_created_by = AuthorizedType(
            id=created_by.id,
            email=created_by.email,
            first_name=created_by.first_name,
            last_name=created_by.last_name,
            created_at=created_by.created_at,
            updated_at=created_by.updated_at,
            role=created_by.role,
            status=created_by.status,
            chat_identity=created_by.chat_identity,
        )

        return output_created_by

    @strawberry.field
    async def program(self, info) -> Optional[ProgramType]:
        program = await self.load_program()

        output_program = ProgramType(
            id=program.id,
            title=program.title,
            description=program.description,
            created_at=program.created_at,
            updated_at=program.updated_at,
        )

        return output_program


@strawberry.type
class CohortsListType:
    cohorts: list[CohortType]
    total_pages: int


@strawberry.type
class CohortTypeConnection:
    items: list[CohortType]
    page_info: PageInfoType


@strawberry.type
class AdminNotFoundError:
    message: str
    schedule_id: int


@strawberry.type
class CohortInfoType:
    id: UUID
    name: str
    program_id: UUID
    limit: int
    created_at: datetime
    updated_at: datetime
    program: ProgramType
    created_by: AuthorizedType
    current_week: Optional[str] = None
    current_module: Optional[str] = None
    total_weeks: int
