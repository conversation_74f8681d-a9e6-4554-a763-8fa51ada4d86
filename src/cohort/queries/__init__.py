import math
from typing import List, Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import (
    CohortMembersRepository,
    CohortRepository,
)

from src.log.logging import logger
from src.cohort.inputs import (
    CohortIDInput,
    CohortMemberIDInput,
)
from src.cohort.queries.get_cohort import get_cohort
from src.cohort.queries.get_cohorts import get_cohorts as get_paginated_cohorts
from src.cohort.queries.get_cohort_by_participant_id import (
    get_cohort_by_participant_id,
)
from src.cohort.types import (
    CohortMembersListType,
    CohortMemberType,
    CohortsListType,
    CohortType,
)
from src.common.input import PaginationInput
from src.settings import get_settings

settings = get_settings()


async def get_cohort_ids(
    info, pagination: Optional[PaginationInput] = PaginationInput()
) -> List[UUID]:
    if pagination is None:
        raise NotImplementedError("Pagination is required")

    return await CohortRepository.get_all_cohort_ids(
        pagination.page, pagination.per_page
    )


async def get_cohorts(
    info, pagination: Optional[PaginationInput] = PaginationInput()
) -> CohortsListType:
    try:
        total_cohorts = await CohortRepository.get_cohorts_count()
        total_pages = math.ceil(total_cohorts / pagination.per_page)
        cohorts_output = await CohortRepository.get_cohorts(
            pagination.page, pagination.per_page
        )
        logger.info(f"Got {len(cohorts_output)} cohorts")
        return CohortsListType(cohorts=cohorts_output, total_pages=total_pages)
    except Exception as e:
        logger.exception(e)
        return CohortsListType(cohorts=[], total_pages=0)


async def get_cohort_member(
    data: CohortMemberIDInput,
) -> Optional[CohortMemberType]:
    try:
        cohort_member = await CohortMembersRepository.get_cohort_member(
            data.participant_id
        )
        return cohort_member
    except Exception as e:
        logger.exception(e)
        return None


async def get_cohort_members(data: CohortIDInput) -> CohortMembersListType:
    try:
        cohort_members = await CohortMembersRepository.get_one_cohort_members(
            data.id
        )
        return CohortMembersListType(
            cohort_members=cohort_members.cohort_members
        )
    except Exception as e:
        logger.exception(e)
        return CohortMembersListType(cohort_members=[])


async def get_all_cohort_members() -> CohortMembersListType:
    try:
        cohort_members = await CohortMembersRepository.get_all_cohorts_members(
            1, 10
        )
        return CohortMembersListType(
            cohort_members=cohort_members.cohort_members
        )
    except Exception as e:
        logger.exception(e)
        return CohortMembersListType(cohort_members=[])


@strawberry.type()
class CohortQuery:
    get_cohort_ids: List[UUID] = strawberry.field(resolver=get_cohort_ids)
    get_cohorts: CohortsListType = strawberry.field(
        resolver=get_paginated_cohorts
    )
    get_cohort: Optional[CohortType] = strawberry.field(resolver=get_cohort)
    get_cohort_member: Optional[CohortMemberType] = strawberry.field(
        resolver=get_cohort_member
    )
    get_cohort_members: CohortMembersListType = strawberry.field(
        resolver=get_cohort_members
    )
    get_all_cohort_members: CohortMembersListType = strawberry.field(
        resolver=get_all_cohort_members
    )
    get_cohort_by_participant_id: Optional[CohortType] = strawberry.field(
        resolver=get_cohort_by_participant_id
    )
