import json
from typing import List, Optional
from uuid import UUID

import strawberry
from ciba_participant.common.aws_handler import generate_presigned_url
from ciba_participant.program.crud import (
    ProgramModuleRepository,
    ProgramModuleSectionRepository,
    ProgramRepository,
)
from ciba_participant.settings import get_settings
from src.log.logging import logger

from src.program.inputs import (
    GetProgramInput,
    GetProgramMediaInput,
    ProgramModuleIDInput,
    ProgramModuleSectionIDInput,
)
from src.program.types import (
    MediaUploadUrlType,
    ProgramModuleSectionType,
    ProgramModuleType,
    ProgramsType,
    ProgramType,
)

settings = get_settings()


async def get_program_ids() -> List[UUID]:
    return await ProgramRepository.get_all_program_ids()


async def get_program(info, data: GetProgramInput) -> Optional[ProgramType]:
    try:
        logger.info(f"Fetching program with id {data.id}")
        program = await ProgramRepository.get_program(data.id)
        if not program:
            return None
        logger.info(f"Fetched program with id {data.id}")

        return ProgramType(
            id=program.id,
            created_at=program.created_at,
            title=program.title,
            updated_at=program.updated_at,
            description=program.description,
            _modules=program.modules,
        )
    except Exception as e:
        logger.exception(e)
        raise e


async def get_programs(info) -> ProgramsType:
    try:
        programs = await ProgramRepository.get_programs(1, 100)

        programs_output: list[ProgramType | None] = []

        for program in programs:
            programs_output.append(
                ProgramType(
                    id=program.id,
                    created_at=program.created_at,
                    title=program.title,
                    updated_at=program.updated_at,
                    description=program.description,
                    _modules=program.modules,
                )
            )

        return ProgramsType(programs=programs_output)
    except Exception as e:
        logger.exception(e)
        return ProgramsType(programs=[])


async def get_media_upload_url(
    data: GetProgramMediaInput,
) -> MediaUploadUrlType:
    try:
        section = (
            await ProgramModuleSectionRepository.get_program_module_section(
                data.section_id
            )
        )
        if not section:
            raise Exception(f"Section with id {data.section_id} not found")
        module = await ProgramModuleRepository.get_program_module(
            section.program_module_id
        )
        if not module:
            raise Exception(
                f"Module with id {section.program_module_id} not found"
            )
        program = await ProgramRepository.get_program(module.program_id)
        if not program:
            raise Exception(f"Program with id {module.program_id} not found")

        path2content_type = {
            "video/mp4": "video/src/",
            "video/quicktime": "video/src/",
            "application/pdf": "pdf/",
            "text/html": "html/",
        }
        object_name = f"programs/{program.title}/{path2content_type[data.content_type]}{data.file_name}"

        logger.info(
            f"Generating presigned url for {object_name} in bucket {settings.AWS_BUCKET_NAME}"
        )

        presigned_url = generate_presigned_url(
            bucket_name=settings.AWS_BUCKET_NAME,
            object_name=object_name,
            content_type=data.content_type,
            expiration=3600,
            region_name="us-east-2",
        )
        return MediaUploadUrlType(
            url=presigned_url["url"],
            fields=json.dumps(presigned_url["fields"]),
        )
    except Exception as e:
        logger.exception(e)
        raise e


async def get_program_modules() -> list[ProgramModuleType]:
    try:
        modules = await ProgramModuleRepository.get_program_modules(1, 10)

        modules_output: list[ProgramModuleType] = []

        for module in modules:
            modules_output.append(
                ProgramModuleType(
                    id=module.id,
                    created_at=module.created_at,
                    updated_at=module.updated_at,
                    title=module.title,
                    short_title=module.short_title,
                    length=module.length,
                    description=module.description,
                    program_id=module.program_id,
                    order=module.order,
                )
            )

        return modules_output
    except Exception as e:
        logger.exception(e)
        return []


async def get_program_module(
    data: ProgramModuleIDInput,
) -> Optional[ProgramModuleType]:
    try:
        module = await ProgramModuleRepository.get_program_module(data.id)
        if not module:
            return None

        return ProgramModuleType(
            id=module.id,
            created_at=module.created_at,
            updated_at=module.updated_at,
            title=module.title,
            short_title=module.short_title,
            length=module.length,
            description=module.description,
            program_id=module.program_id,
            order=module.order,
        )
    except Exception as e:
        logger.exception(e)
        return None


async def get_program_module_sections() -> List[ProgramModuleSectionType]:
    try:
        sections = (
            await ProgramModuleSectionRepository.get_program_module_sections(
                1, 10
            )
        )
        return [
            ProgramModuleSectionType(
                id=section.id,
                created_at=section.created_at,
                updated_at=section.updated_at,
                title=section.title,
                description=section.description,
                raw_metadata=section.metadata,
                program_module_id=section.program_module_id,
                activity_type=section.activity_type,
                activity_category=section.activity_category,
            )
            for section in sections
        ]
    except Exception as e:
        logger.exception(e)
        return []


async def get_program_module_section(
    data: ProgramModuleSectionIDInput,
) -> Optional[ProgramModuleSectionType]:
    try:
        section = (
            await ProgramModuleSectionRepository.get_program_module_section(
                data.id
            )
        )

        if not section:
            return None

        return ProgramModuleSectionType(
            id=section.id,
            created_at=section.created_at,
            updated_at=section.updated_at,
            title=section.title,
            description=section.description,
            raw_metadata=section.metadata,
            program_module_id=section.program_module_id,
            activity_type=section.activity_type,
            activity_category=section.activity_category,
        )
    except Exception as e:
        logger.exception(e)
        return None


@strawberry.type()
class ProgramQuery:
    """Program graphql queries."""

    get_program_ids: List[UUID] = strawberry.field(resolver=get_program_ids)
    get_programs: ProgramsType = strawberry.field(resolver=get_programs)
    get_program: Optional[ProgramType] = strawberry.field(resolver=get_program)
    get_media_upload_url: MediaUploadUrlType = strawberry.field(
        resolver=get_media_upload_url
    )
    get_program_modules: list[ProgramModuleType] = strawberry.field(
        resolver=get_program_modules
    )
    get_program_module: Optional[ProgramModuleType] = strawberry.field(
        resolver=get_program_module
    )
    get_program_module_sections: List[ProgramModuleSectionType] = (
        strawberry.field(resolver=get_program_module_sections)
    )
    get_program_module_section: Optional[ProgramModuleSectionType] = (
        strawberry.field(resolver=get_program_module_section)
    )
