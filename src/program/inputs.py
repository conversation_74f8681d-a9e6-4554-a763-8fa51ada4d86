from uuid import UUID
import strawberry
from typing import Optional
from datetime import datetime
from src.activity.types import PATypeGraphEnum, PACategoryGraphEnum
from src.program.types import MetaTypeGraphEnum


@strawberry.input
class ProgramModuleSectionMetadataInput:
    started_at: Optional[datetime] | None = None
    url: Optional[str] | None = None
    form_id: Optional[str] | None = None
    type: Optional["MetaTypeGraphEnum"] | None = None


@strawberry.input
class ProgramModuleInput:
    description: str
    program_id: UUID
    short_title: str
    title: str
    length: int


@strawberry.input
class ProgramCreateInput:
    title: str
    description: Optional[str] = ""


@strawberry.input
class ProgramUpdateInput:
    id: UUID
    title: Optional[str] = None
    description: Optional[str] = None


@strawberry.input
class GetProgramInput:
    id: UUID


@strawberry.input
class GetProgramMediaInput:
    section_id: UUID
    content_type: str
    file_name: str


@strawberry.input
class ProgramModuleCreateInput:
    title: str
    short_title: str
    length: int
    description: str
    program_id: UUID
    order: int


@strawberry.input
class ProgramModuleUpdateInput:
    id: UUID
    title: Optional[str] = None
    short_title: Optional[str] = None
    length: Optional[int] = None
    description: Optional[str] = None
    program_id: Optional[UUID] = None


@strawberry.input
class ProgramModuleIDInput:
    id: UUID


@strawberry.input
class ProgramModuleSectionCreateInput:
    title: str
    description: str
    metadata: ProgramModuleSectionMetadataInput
    program_module_id: UUID
    activity_type: PATypeGraphEnum
    activity_category: PACategoryGraphEnum


@strawberry.input
class ProgramModuleSectionUpdateInput:
    id: UUID
    title: Optional[str] = None
    description: Optional[str] = None
    metadata: Optional[ProgramModuleSectionMetadataInput] = None
    program_module_id: Optional[UUID] = None
    activity_type: Optional[PATypeGraphEnum] = None
    activity_category: Optional[PACategoryGraphEnum] = None


@strawberry.input
class ProgramModuleSectionIDInput:
    id: UUID
