from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.program.crud import ProgramModuleRepository
from ciba_participant.program.models import (
    FullProgramModule,
    MetaType,
    RawProgramModuleSection,
)
from ciba_participant.program.pydantic_models import (
    ProgramModuleSectionMetadata,
    get_presigned_url,
)

from src.activity.types import PACategoryGraphEnum, PATypeGraphEnum
from src.settings import get_settings

settings = get_settings()

MetaTypeGraphEnum = strawberry.enum(MetaType)


@strawberry.type
class ProgramModuleSectionMetadataType:
    url: Optional[str]
    form_id: Optional[str]
    started_at: Optional[datetime]
    type: Optional[MetaTypeGraphEnum]  # type: ignore
    signed_url: Optional[str]


@strawberry.type
class ProgramModuleSectionType:
    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    description: str
    program_module_id: UUID
    activity_type: PATypeGraphEnum  # type: ignore
    activity_category: PACategoryGraphEnum  # type: ignore

    raw_metadata: strawberry.Private[
        ProgramModuleSectionMetadata | dict | None
    ] = None

    @strawberry.field
    def metadata(self) -> ProgramModuleSectionMetadataType | None:
        raw_metadata = self.raw_metadata

        if raw_metadata is None:
            return None

        if isinstance(raw_metadata, dict):
            metadata = ProgramModuleSectionMetadata.model_validate(
                raw_metadata
            )
        else:
            metadata = raw_metadata

        signed_url = metadata.signed_url

        if signed_url is None:
            signed_url = get_presigned_url(metadata.url)

        return ProgramModuleSectionMetadataType(
            started_at=metadata.started_at,
            url=metadata.url,
            form_id=metadata.form_id,
            type=metadata.type,
            signed_url=signed_url,
        )


@strawberry.type
class ProgramModuleType:
    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    short_title: str
    length: int
    description: str
    program_id: UUID
    order: int
    started_at: Optional[datetime] = None  # Added field
    ended_at: Optional[datetime] = None  # Added field
    cohort_module_id: Optional[UUID] = None  # Added field

    _sections: strawberry.Private[list[RawProgramModuleSection] | None] = None

    async def load_sections(self) -> list[RawProgramModuleSection]:
        if self._sections is None:
            self._sections = (
                await ProgramModuleRepository.get_program_module_sections(
                    self.id
                )
            )
        return self._sections

    @strawberry.field
    async def sections(self) -> list[ProgramModuleSectionType]:
        sections = await self.load_sections()

        if self._sections is None:
            return []

        sections_output: list[ProgramModuleSectionType] = []

        for section in sections:
            sections_output.append(
                ProgramModuleSectionType(
                    id=section.id,
                    created_at=section.created_at,
                    updated_at=section.updated_at,
                    title=section.title,
                    description=section.description,
                    raw_metadata=section.metadata,
                    program_module_id=section.program_module_id,
                    activity_type=section.activity_type,
                    activity_category=section.activity_category,
                )
            )

        return sections_output


@strawberry.type
class ProgramType:
    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    description: str

    _modules: strawberry.Private[list[FullProgramModule] | None] = None

    async def load_modules(self) -> list[FullProgramModule]:
        if self._modules is None:
            self._modules = (
                await ProgramModuleRepository.get_program_modules_by_program(
                    self.id
                )
            )

        return self._modules

    @strawberry.field
    async def has_sections(self) -> bool:
        return await ProgramModuleRepository.has_modules(self.id)

    @strawberry.field
    async def has_cohorts(self) -> bool:
        return await CohortRepository.any_cohorts_by_program(self.id)

    @strawberry.field
    async def modules(self) -> list[ProgramModuleType]:
        modules = await self.load_modules()

        if modules is None:
            return []

        modules_output: list[ProgramModuleType] = []

        for module in modules:
            modules_output.append(
                ProgramModuleType(
                    id=module.id,
                    created_at=module.created_at,
                    description=module.description,
                    length=module.length,
                    order=module.order,
                    program_id=module.program_id,
                    short_title=module.short_title,
                    title=module.title,
                    updated_at=module.updated_at,
                    _sections=module.sections,
                )
            )

        return modules_output


@strawberry.type
class ProgramsType:
    programs: list[ProgramType | None] | None


@strawberry.type
class MediaUploadUrlType:
    url: str
    fields: str
