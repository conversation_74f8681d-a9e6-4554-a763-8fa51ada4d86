from uuid import UUID

from ciba_participant.classes.crud import LiveSessionRepository
from src.log.logging import logger

from src.common.types import SimpleRespType


async def upload_live_session_recording(
    live_session_id: UUID,
) -> SimpleRespType:
    try:
        logger.info(f"Uploading recording for live session {live_session_id}")

        await LiveSessionRepository.upload_live_session_recording(
            live_session_id
        )

        logger.info(f"Uploaded recording for live session {live_session_id}")

        live_session = await LiveSessionRepository.get_live_session(
            live_session_id
        )

        if not live_session:
            logger.warning(f"Live session {live_session_id} not found")
            raise Exception("Live session not found")

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        raise e
