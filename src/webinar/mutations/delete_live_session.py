from uuid import UUID

from ciba_participant.classes.crud import LiveSessionRepository
from src.log.logging import logger

from src.common.types import ShortResponse
from ciba_participant.classes.errors import LiveSessionError
from ciba_participant.schedule_manager.types import ScheduleManagerError


UNEXPECTED_ERROR = "Unexpected Error. Please contact support."


async def delete_live_session(
    live_session_id: UUID,
) -> ShortResponse:
    try:
        logger.info(f"Deleting live session {live_session_id}")

        await LiveSessionRepository.delete_live_session(live_session_id)

        return ShortResponse(success=True)

    except LiveSessionError as e:
        logger.exception(e)
        return ShortResponse(
            success=False,
            error=e.message if hasattr(e, "message") else UNEXPECTED_ERROR,
        )
    except ScheduleManagerError as e:
        logger.exception(e)
        return ShortResponse(
            success=False,
            error=e.args[0] if hasattr(e, "args") else UNEXPECTED_ERROR,
        )
    except Exception:
        return ShortResponse(success=False, error=UNEXPECTED_ERROR)
