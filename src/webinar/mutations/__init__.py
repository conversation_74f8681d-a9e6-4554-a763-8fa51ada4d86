from uuid import UUID

import strawberry
from ciba_participant.classes.crud import WebinarRepository
from ciba_participant.classes.crud.create_webinar import (
    WebinarCreate,
    HostPermissionError,
)
from ciba_participant.classes.crud.update_webinar import WebinarUpdate
from src.webinar.mutations.update_live_session import update_live_session
from src.webinar.mutations.upload_live_session_recording import (
    upload_live_session_recording,
)
from src.webinar.mutations.delete_live_session_recording import (
    delete_live_session_recording,
)
from src.webinar.mutations.delete_live_session import (
    delete_live_session,
)
from src.webinar.mutations.upload_video_recording import upload_video_recording
from src.webinar.mutations.update_live_session_recording import (
    update_live_session_recording,
)

from ciba_participant.schedule_manager.types import ScheduleManagerError
from src.webinar.inputs import (
    CreateClassInput,
    UpdateClassInput,
)
from src.common.types import SimpleRespType, ShortResponse
from src.webinar.types import WebinarCRUDResponse
from src.log.logging import logger


async def delete_webinar(class_id: UUID, user_id: UUID) -> WebinarCRUDResponse:
    """
    resolver to handle the deletion of a class
    """
    try:
        await WebinarRepository.delete_webinar(
            webinar_id=class_id, user_id=user_id
        )

        return WebinarCRUDResponse(
            webinar_id=class_id, success=True, error=None
        )
    except (ScheduleManagerError, Exception) as e:
        logger.error(e)
        return WebinarCRUDResponse(
            webinar_id=class_id, success=False, error=str(e)
        )


async def create_webinar(
    input: CreateClassInput, user_id: UUID
) -> WebinarCRUDResponse:
    """
    resolver to handle the creation of a class
    """
    if input.number_of_sessions > 100:
        return WebinarCRUDResponse(
            error="Maximum session limit (100) exceeded.",
            success=False,
        )

    try:
        webinar_id = await WebinarRepository.create_webinar(
            data=WebinarCreate(**vars(input)), user_id=user_id
        )
        return WebinarCRUDResponse(
            webinar_id=webinar_id, error=None, success=True
        )

    except HostPermissionError as e:
        logger.warning(e)
        return WebinarCRUDResponse(
            webinar_id=None, success=False, error=str(e)
        )

    except (ScheduleManagerError, Exception) as e:
        logger.error(e)
        return WebinarCRUDResponse(
            webinar_id=None, success=False, error=str(e)
        )


async def update_webinar(
    input: UpdateClassInput, user_id: UUID
) -> WebinarCRUDResponse:
    """
    resolver to handle the update of a class
    """
    try:
        await WebinarRepository.update_webinar(
            data=WebinarUpdate(**vars(input)), user_id=user_id
        )
        return WebinarCRUDResponse(
            webinar_id=input.id, error=None, success=True
        )
    except (ScheduleManagerError, Exception) as e:
        logger.error(e)
        return WebinarCRUDResponse(
            webinar_id=input.id, success=False, error=str(e)
        )


@strawberry.type()
class WebinarMutation:
    """Webinars graphql mutations."""

    create_webinar: WebinarCRUDResponse = strawberry.field(
        resolver=create_webinar
    )
    update_webinar: WebinarCRUDResponse = strawberry.field(
        resolver=update_webinar
    )
    delete_webinar: WebinarCRUDResponse = strawberry.field(
        resolver=delete_webinar
    )
    update_live_session: SimpleRespType = strawberry.field(
        resolver=update_live_session
    )
    delete_live_session: ShortResponse = strawberry.field(
        resolver=delete_live_session
    )
    upload_live_session_recording: SimpleRespType = strawberry.field(
        resolver=upload_live_session_recording
    )
    delete_live_session_recording: SimpleRespType = strawberry.field(
        resolver=delete_live_session_recording
    )
    upload_video_recording: SimpleRespType = strawberry.field(
        resolver=upload_video_recording
    )
    update_live_session_recording: SimpleRespType = strawberry.field(
        resolver=update_live_session_recording
    )
