from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry
from ciba_participant.classes.crud import LiveSessionRepository
from src.log.logging import logger

from src.common.types import SimpleRespType


@strawberry.input
class LiveSessionUpdateInput:
    title: Optional[str] = None
    description: Optional[str] = None
    start_time: Optional[datetime] = None
    timezone: Optional[str] = None
    use_custom_meeting_link: Optional[bool] = None
    custom_meeting_link: Optional[str] = None


async def update_live_session(
    live_session_id: UUID, data: LiveSessionUpdateInput
) -> SimpleRespType:
    try:
        logger.info(f"Updating live session {live_session_id}")

        await LiveSessionRepository.update_live_session(
            live_session_id,
            title=data.title,
            description=data.description,
            start_time=data.start_time,
            timezone=data.timezone,
            use_custom_meeting_link=data.use_custom_meeting_link,
            custom_meeting_link=data.custom_meeting_link,
        )

        logger.info(f"Updated live session {live_session_id}")

        live_session = await LiveSessionRepository.get_live_session(
            live_session_id
        )

        if not live_session:
            raise Exception("Live session not found")

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)
