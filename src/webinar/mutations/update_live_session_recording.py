from uuid import UUID

from ciba_participant.classes.crud import LiveSessionRepository
from src.log.logging import logger

from src.common.types import SimpleRespType


async def update_live_session_recording(
    live_session_id: UUID, video_url: str
) -> SimpleRespType:
    try:
        logger.info(f"Updating recording for live session {live_session_id}")

        await LiveSessionRepository.update_live_session_recording(
            live_session_id, video_url
        )

        return SimpleRespType(status=True)

    except Exception as e:
        logger.exception(e)
        return SimpleRespType(status=False)
