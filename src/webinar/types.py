from datetime import datetime
from enum import StrEnum, auto
from typing import Optional, List
from uuid import UUID

import strawberry
from ciba_participant.classes.models import (
    RecurrenceEnum,
    TopicEnum,
    TimeOfDayEnum,
    MeetingTypeEnum,
    BookingStatusEnum,
)

from src.auth import AuthorizedType
from src.participant.types import ParticipantType

ClassTopic = strawberry.enum(TopicEnum)
TimeOfDay = strawberry.enum(TimeOfDayEnum)
Recurrence = strawberry.enum(RecurrenceEnum)
MeetingType = strawberry.enum(MeetingTypeEnum)
BookingStatus = strawberry.enum(BookingStatusEnum)


@strawberry.enum
class FullBookingStatusGraphEnum(StrEnum):
    BOOKED = auto()
    ATTENDED = auto()
    WATCHED_RECORDING = auto()
    CANCELED = auto()
    MISSED = auto()


@strawberry.type
class BookingType:
    id: UUID
    status: BookingStatus  # type: ignore
    participant: Optional[ParticipantType] = None


@strawberry.type
class LiveSessionType:
    id: UUID
    title: str
    description: str
    meeting_start_time: datetime
    host_id: Optional[UUID] = None
    topic: Optional[ClassTopic] = None
    timezone: str
    has_conflict: bool
    meeting_type: MeetingType  # type: ignore
    zoom_id: str
    zoom_occurrence_id: str
    zoom_link: str
    recording_url: Optional[str]
    use_custom_meeting_link: bool
    custom_meeting_link: Optional[str]
    max_capacity: Optional[int] = None
    bookings_count: Optional[int] = None
    bookings: Optional[list[BookingType]] = None
    webinar_id: Optional[UUID] = None


@strawberry.type
class WebinarType:
    id: UUID
    topic: ClassTopic  # type: ignore
    title: str
    description: str
    recurrence: Recurrence  # type: ignore
    host: Optional[AuthorizedType] = None
    duration: int
    max_capacity: int = 250
    cover_url: Optional[str] = None
    featured_session: Optional[LiveSessionType] = None
    live_sessions: List[LiveSessionType]


@strawberry.type
class WebinarListType:
    webinars: List[WebinarType]
    total_pages: int


@strawberry.type
class WebinarCRUDResponse:
    webinar_id: Optional[UUID] = strawberry.field(
        name="webinarId", default=None
    )
    success: bool
    error: Optional[str] = None


@strawberry.type
class LiveSessionListType:
    live_sessions: List[LiveSessionType]
    success: bool
    error: Optional[str] = None


@strawberry.type
class ProviderType:
    id: UUID
    full_name: str
    first_name: str
    last_name: str
    email: str
    chat_identity: str
    description: Optional[str] = None
    avatar_url: Optional[str] = None
    is_admin: Optional[bool] = None
    old_chat_identity: Optional[str] = None


@strawberry.type
class ClassCreatorType:
    success: bool
    creators: List[ProviderType]
    error: Optional[str] = None


@strawberry.type
class PreviewDateType:
    date: datetime
    has_conflict: bool


@strawberry.type
class PreviewDateListType:
    dates: list[PreviewDateType]
