from datetime import datetime
from typing import Optional
from uuid import UUID

import strawberry

from src.webinar.types import (
    ClassTopic,
    Recurrence,
    TimeOfDay,
)


@strawberry.input
class RecurrentDatesInput:
    starting_from: datetime
    timezone: str
    count: int
    recurrence: Recurrence  # type: ignore


@strawberry.input
class ClassFilter:
    search: Optional[str] = None
    topic: Optional[ClassTopic] = None  # type: ignore
    class_admin: Optional[UUID] = None
    time_of_day: Optional[TimeOfDay] = None  # type: ignore


@strawberry.input
class CreateClassInput:
    topic: ClassTopic  # type: ignore
    title: str
    description: str
    max_capacity: int
    timezone: str
    meeting_start_time: datetime
    number_of_sessions: int
    recurrence: Recurrence  # type: ignore
    host_id: UUID
    duration: Optional[int] = 60
    cover_url: Optional[str] = None


@strawberry.input
class UpdateClassInput:
    id: UUID
    topic: Optional[ClassTopic] = None  # type: ignore
    max_capacity: Optional[int] = None
    meeting_start_time: Optional[datetime] = None
    title: Optional[str] = None
    description: Optional[str] = None
    duration: Optional[int] = None
    cover_url: Optional[str] = None
    new_host: Optional[str] = None


@strawberry.input
class WebinarFilterInput:
    title_like: Optional[str] = None
    topic: Optional[ClassTopic] = None  # type: ignore
    webinar_host: Optional[UUID] = None
    time_of_day: Optional[TimeOfDay] = None  # type: ignore
