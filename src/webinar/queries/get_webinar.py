from uuid import UUID

import pendulum
from ciba_participant.classes.crud import WebinarRepository
from src.webinar.types import WebinarType


async def get_webinar(webinar_id: UUID) -> WebinarType:
    """
    Retrieves a webinar given its id.

    Args:
        webinar_id (UUID): UUID of the webinar

    Returns:
        WebinarType: Formatted webinar data


    """
    webinar = await WebinarRepository.get_webinar(webinar_id=webinar_id)

    featured_session = None
    time_now = pendulum.now("UTC")

    if webinar.sessions:
        sorted_sessions = sorted(
            webinar.sessions, key=lambda session: session.meeting_start_time
        )
        # Use next() with a generator and provide sorted_sessions[-1] as default if no upcoming session is found.
        featured_session = next(
            (
                session
                for session in sorted_sessions
                if session.meeting_start_time > time_now
            ),
            sorted_sessions[-1],
        )

    return WebinarType(
        id=webinar.id,
        topic=webinar.topic,
        title=webinar.title,
        description=webinar.description,
        recurrence=webinar.recurrence,
        host=webinar.host,
        duration=webinar.duration,
        max_capacity=webinar.max_capacity,
        cover_url=webinar.cover_url,
        featured_session=featured_session,
        live_sessions=webinar.sessions,
    )
