from datetime import datetime
from typing import Optional

from ciba_participant.classes.crud import LiveSessionRepository
from tortoise.exceptions import BaseORMException

from ciba_participant.classes.models import RawLiveSession
from src.content_library.messages import DB_READ_ERROR
from src.webinar.inputs import WebinarFilterInput
from src.webinar.types import LiveSessionListType, LiveSessionType


async def get_live_sessions(
    start_date: datetime,
    end_date: datetime,
    timezone: Optional[str] = "UTC",
    filters: Optional[WebinarFilterInput] = None,
) -> LiveSessionListType:
    """
    Method to get live sessions for a time range.
    :param start_date: Start date for live sessions search
    :param end_date: End date for live sessions search
    :param timezone: Timezone for live sessions search
    :param filters: Filters for live sessions search
    :return: List of found live sessions
    """
    if not start_date or not end_date:
        return LiveSessionListType(
            live_sessions=[],
            success=False,
            error="Start and end date must be specified.",
        )

    try:
        sessions = await LiveSessionRepository.get_live_sessions(
            start_date=start_date,
            end_date=end_date,
            timezone=timezone,
            filters=filters,
        )

        return LiveSessionListType(
            live_sessions=[parse_session(session) for session in sessions],
            success=True,
        )
    except ValueError as error:
        return LiveSessionListType(
            live_sessions=[],
            success=False,
            error=str(error),
        )
    except BaseORMException:
        return LiveSessionListType(
            live_sessions=[],
            success=False,
            error=DB_READ_ERROR,
        )


def parse_session(session: RawLiveSession) -> LiveSessionType:
    """
    Method to parse a raw live session into a LiveSessionType.
    """
    return LiveSessionType(
        id=session.id,
        webinar_id=session.webinar_id,
        title=session.title,
        description=session.description,
        meeting_start_time=session.meeting_start_time,
        host_id=session.host_id,
        topic=session.topic,
        timezone=session.timezone,
        has_conflict=session.has_conflict,
        meeting_type=session.meeting_type,
        zoom_id=session.zoom_id,
        zoom_occurrence_id=session.zoom_occurrence_id,
        zoom_link=session.zoom_link,
        recording_url=session.recording_url,
        use_custom_meeting_link=session.use_custom_meeting_link,
        custom_meeting_link=session.custom_meeting_link,
        max_capacity=session.max_capacity,
        bookings_count=session.bookings_count,
    )
