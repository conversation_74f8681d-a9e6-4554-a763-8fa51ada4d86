from typing import Optional

import pendulum
from strawberry import Info
from ciba_participant.classes.crud import WebinarRepository
from ciba_participant.classes.crud.get_webinars import FilterInput, Include
from ciba_participant.classes.models import FullWebinar

from src.webinar.inputs import WebinarFilterInput
from src.log.logging import logger
from src.common.input import PaginationInput
from src.webinar.types import WebinarType, WebinarListType


async def get_webinars(
    info: Info,
    pagination: Optional[PaginationInput] = PaginationInput(),
    filters: Optional[WebinarFilterInput] = None,
    timezone: Optional[str] = "America/Los_Angeles",
) -> WebinarListType:
    """
    Retrieves a paginated list of webinars with applied filters and additional details.

    This function fetches webinars, applies filters if provided,
    and formats the data to include detailed information about each webinar,
    including featured and live session details. If pagination is not provided,
    a NotImplementedError will be raised as pagination is mandatory.

    Args:
        info (Info): Strawberry info object.
        pagination (Optional[PaginationInput]): Pagination settings, such as page number
            and items per page. Defaults to an instance of PaginationInput.
        filters (Optional[ClassFilter]): Optional filters to apply while retrieving webinars.

    Returns:
        WebinarListType: A structured list of formatted webinar data including detailed
        information, total pages information for pagination.

    Raises:
        NotImplementedError: If the pagination parameter is None.
    """
    include = {Include.sessions, Include.host, Include.bookings}
    if pagination is None:
        raise NotImplementedError("Pagination is required")

    filters_input = None

    if filters:
        filters_input = FilterInput.model_validate(
            filters, from_attributes=True
        )

    paginated_webinars = await WebinarRepository.get_webinars(
        page=pagination.page,
        per_page=pagination.per_page,
        include=include,
        filters=filters_input,
        timezone_str=timezone,
    )

    webinars: list[FullWebinar] = paginated_webinars.webinars

    webinars_output: list[WebinarType] = []

    for webinar in webinars:
        time_now = pendulum.now("UTC")

        featured_session = None
        if webinar.sessions:
            sorted_sessions = sorted(
                webinar.sessions,
                key=lambda session: session.meeting_start_time,
            )
            # Use next() with a generator and provide sorted_sessions[-1] as default if no upcoming session is found.
            featured_session = next(
                (
                    session
                    for session in sorted_sessions
                    if session.meeting_start_time > time_now
                ),
                sorted_sessions[-1],
            )

        webinars_output.append(
            WebinarType(
                id=webinar.id,
                topic=webinar.topic,
                title=webinar.title,
                description=webinar.description,
                recurrence=webinar.recurrence,
                host=webinar.host,
                duration=webinar.duration,
                max_capacity=webinar.max_capacity,
                cover_url=webinar.cover_url,
                featured_session=featured_session,
                live_sessions=webinar.sessions,
            )
        )

    logger.info(f"Got {len(webinars_output)} webinars")

    return WebinarListType(
        webinars=webinars_output, total_pages=paginated_webinars.total_pages
    )
