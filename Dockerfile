# Base stage
FROM ghcr.io/astral-sh/uv:python3.12-alpine AS base

WORKDIR /app

ENV UV_PROJECT_ENVIRONMENT=/usr/local

# Enable bytecode compilation
ENV UV_COMPILE_BYTECODE=1

# Copy from the cache instead of linking since it's a mounted volume
ENV UV_LINK_MODE=copy

# https://docs.python.org/3/using/cmdline.html#envvar-PYTHONDONTWRITEBYTECODE
# Prevents Python from writing .pyc files to disk
ENV PYTHONDONTWRITEBYTECODE=1

# ensures that the python output is sent straight to terminal (e.g. your container log)
# without being first buffered and that you can see the output of your application (e.g. django logs)
# in real time. Equivalent to python -u: https://docs.python.org/3/using/cmdline.html#cmdoption-u
ENV PYTHONUNBUFFERED=1
ENV TESTING=0

RUN apk update && apk --no-cache add \
    bash \
    build-base \
    cargo \
    curl \
    git \
    libffi-dev \
    musl-dev \
    openssh-client \
    openssl-dev \
    python3-dev \
    rust \
    && apk cache clean


# SSH configuration for private repositories
RUN mkdir -p -m 0700 ~/.ssh \
    && ssh-keyscan github.com >> ~/.ssh/known_hosts


# Install the project's dependencies using the lockfile and settings
RUN --mount=type=ssh \
    --mount=type=cache,target=/root/.cache/uv \
    --mount=type=bind,source=uv.lock,target=uv.lock \
    --mount=type=bind,source=pyproject.toml,target=pyproject.toml \
    uv sync --frozen --no-install-project --no-dev

# # Then, add the rest of the project source code and install it
# # Installing separately from its dependencies allows optimal layer caching
ADD . /app
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-dev

# # Place executables in the environment at the front of the path
ENV PATH="/app/.venv/bin:$PATH"

# Development stage
FROM base AS dev

# Additional dependencies for development
RUN apk --no-cache add \
    build-base \
    openssh-server \
    && apk cache clean

# Prevents Python from writing .pyc files to disk
ENV TESTING=0
ENV ENV=dev

# Copy entrypoint script and ensure it's executable
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000 22 5678 3005

# Pytest stage for running tests
FROM base AS test

# Set the environment variable for testing
ENV TESTING=1

# Install test dependencies
RUN uv sync --frozen --extra test

# Run pytest
CMD ["uv", "run", "pytest", "--disable-warnings"]

# SSH configuration for runtime
RUN mkdir -p -m 0700 ~/.ssh \
    && ssh-keyscan github.com >> ~/.ssh/known_hosts

# Copy entrypoint script and ensure it's executable
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000 22 5678 3005

# Production stage
FROM base AS prod

ENV ENV=prod
ENV TESTING=0
ENV DEBUG=0

COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

EXPOSE 8000
