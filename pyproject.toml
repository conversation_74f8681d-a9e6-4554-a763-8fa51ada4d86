[build-system]
requires = ["setuptools"]

[project]
name = "ciba-participant-admin"
version = "3.2.2"
description = "One place to rule all data"
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "asgi-correlation-id>=4.3.4",
    "asyncstdlib>=3.12.5",
    "ciba-participant",
    "commitizen>4.6.0",
    "fastapi[standard]>=0.115.12",
    "libcst>=1.4.0",
    "loguru>=0.7.2",
    "pandas>=2.2.2",
    "pendulum>=3.1.0",
    "pre-commit>=4.1.0",
    "psycopg>=3.1.19",
    "psycopg-binary>=3.2.2",
    "psycopg-pool>=3.2.2",
    "psycopg2-binary>=2.9.9",
    "pyjwt>=2.9.0",
    "python-dateutil>=2.9.0.post0",
    "python-multipart>=0.0.18",
    "sentry-sdk>=2.10.0",
    "sqlalchemy>=2.0.31",
    "strawberry-graphql>=0.243.0",
    "uvicorn>=0.30.6",
]


[tool.uv.sources]
ciba-participant = { git = "ssh://**************/Cibahealth/ciba-participant.git", rev = "main" }

[tool.uv]
dev-dependencies = [
    "coverage>=7.6.1",
    "debugpy>=1.8.2",
    "mimesis>=17.0.0",
    "pyright>=1.1.379",
    "pytest>=8.2.2",
    "pytest>=8.2.2",
    "pytest-asyncio>=0.23.7",
    "pytest-asyncio>=0.23.7",
    "pytest-cov>=6.1.1",
    "ruff>=0.9.7",
    "testcontainers[postgres]>=4.8.1",
]

[tool.aerich]
tortoise_orm = "ciba_participant.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."


[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra"
testpaths = [
    "tests",
]
asyncio_default_fixture_loop_scope = "session"
filterwarnings = [
    "ignore::DeprecationWarning",
]


[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "1.0.0"
version_scheme = "pep440"
version_provider = "pep621"
update_changelog_on_bump = true
version = "1.0.13"
version_files = ["src/__version__.py"]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 79
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"


[tool.mypy]
disable_error_code = ["import-untyped"]
